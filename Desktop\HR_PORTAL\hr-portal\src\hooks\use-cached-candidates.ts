import { useCandidates, useCandidatesCacheStatus } from '@/contexts/candidates-context';
import { candidatesData } from '@/data/candidate-data';

/**
 * Hook to get candidates with automatic fallback to sample data
 * This hook provides a simple interface for components that need candidate data
 */
export function useCachedCandidates() {
  const {
    candidates: apiCandidates,
    loading,
    error,
    refreshCandidates,
    fetchCandidates,
    lastFetched,
  } = useCandidates();

  const { hasCache, isCacheValid } = useCandidatesCacheStatus();

  // Use API data if available, otherwise fallback to sample data
  const candidates = apiCandidates.length > 0 ? apiCandidates : candidatesData;

  // Function to ensure fresh data (will use cache if valid, otherwise fetch)
  const ensureFreshData = async () => {
    if (!isCacheValid) {
      await fetchCandidates();
    }
  };

  // Function to force refresh data
  const forceRefresh = async () => {
    await refreshCandidates();
  };

  return {
    candidates,
    loading,
    error,
    hasCache,
    isCacheValid,
    lastFetched,
    ensureFreshData,
    forceRefresh,
    isUsingFallbackData: apiCandidates.length === 0 && candidates.length > 0,
  };
}
