/**
 * WhatsApp Integration Utilities
 * Provides functions to open WhatsApp chat with phone numbers
 */

/**
 * Format phone number for WhatsApp
 * Removes all non-numeric characters and ensures proper format
 */
export function formatPhoneForWhatsApp(phone: string): string {
  // Remove all non-numeric characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  // If phone starts with country code, use as is
  // If it's a 10-digit number, assume it's Indian and add +91
  if (cleanPhone.length === 10) {
    return `91${cleanPhone}`;
  }
  
  // If it already has country code, use as is
  if (cleanPhone.length > 10) {
    return cleanPhone;
  }
  
  return cleanPhone;
}

/**
 * Generate WhatsApp chat URL
 */
export function generateWhatsAppURL(phone: string, message?: string): string {
  const formattedPhone = formatPhoneForWhatsApp(phone);
  const encodedMessage = message ? encodeURIComponent(message) : '';
  
  // Use WhatsApp Web URL format
  const baseURL = 'https://wa.me/';
  
  if (encodedMessage) {
    return `${baseURL}${formattedPhone}?text=${encodedMessage}`;
  }
  
  return `${baseURL}${formattedPhone}`;
}

/**
 * Open WhatsApp chat in new window/tab
 */
export function openWhatsAppChat(phone: string, message?: string): void {
  const url = generateWhatsAppURL(phone, message);
  window.open(url, '_blank', 'noopener,noreferrer');
}

/**
 * Generate default message templates for HR communication
 */
export const WhatsAppTemplates = {
  interview: (candidateName: string, jobTitle: string, date: string, time: string) => 
    `Hi ${candidateName}, 

We would like to schedule an interview for the ${jobTitle} position. 

Proposed Date: ${date}
Time: ${time}

Please confirm your availability. 

Best regards,
HR Team`,

  followUp: (candidateName: string) => 
    `Hi ${candidateName}, 

Hope you're doing well! We wanted to follow up on your application status. 

Could you please share an update on your availability?

Best regards,
HR Team`,

  offer: (candidateName: string, jobTitle: string) => 
    `Hi ${candidateName}, 

Congratulations! We are pleased to extend an offer for the ${jobTitle} position. 

Please let us know when you're available to discuss the details.

Best regards,
HR Team`,

  rejection: (candidateName: string) => 
    `Hi ${candidateName}, 

Thank you for your interest in our company. After careful consideration, we have decided to move forward with other candidates. 

We appreciate your time and wish you the best in your job search.

Best regards,
HR Team`,

  documentRequest: (candidateName: string, documents: string[]) => 
    `Hi ${candidateName}, 

We need the following documents to proceed with your application:

${documents.map(doc => `• ${doc}`).join('\n')}

Please share these at your earliest convenience.

Best regards,
HR Team`,

  general: (candidateName: string) =>
    `Hi ${candidateName},

Hope you're doing well!

Best regards,
HR Team`,

  statusUpdate: (candidateName: string, status: string) =>
    `Hi ${candidateName},

Your application status has been updated to: ${status}

Please let us know if you have any questions.

Best regards,
HR Team`,

  scheduleCall: (candidateName: string) =>
    `Hi ${candidateName},

We would like to schedule a call to discuss your application further.

When would be a good time for you this week?

Best regards,
HR Team`
};

/**
 * Validate phone number format
 */
export function isValidPhoneNumber(phone: string): boolean {
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Should be at least 10 digits
  if (cleanPhone.length < 10) {
    return false;
  }
  
  // Should not be more than 15 digits (international standard)
  if (cleanPhone.length > 15) {
    return false;
  }
  
  return true;
}

/**
 * Format phone number for display
 */
export function formatPhoneForDisplay(phone: string): string {
  const cleanPhone = phone.replace(/\D/g, '');
  
  if (cleanPhone.length === 10) {
    // Format as (XXX) XXX-XXXX
    return `(${cleanPhone.slice(0, 3)}) ${cleanPhone.slice(3, 6)}-${cleanPhone.slice(6)}`;
  }
  
  if (cleanPhone.length === 12 && cleanPhone.startsWith('91')) {
    // Indian number with country code
    const number = cleanPhone.slice(2);
    return `+91 ${number.slice(0, 5)} ${number.slice(5)}`;
  }
  
  // Return with country code format
  if (cleanPhone.length > 10) {
    return `+${cleanPhone}`;
  }
  
  return phone;
}

/**
 * Get WhatsApp status (whether WhatsApp is available)
 */
export function getWhatsAppAvailability(): {
  isAvailable: boolean;
  message: string;
} {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') {
    return {
      isAvailable: false,
      message: 'WhatsApp is not available in server environment'
    };
  }
  
  // Check if device supports WhatsApp
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
  
  return {
    isAvailable: true,
    message: isMobile 
      ? 'WhatsApp will open in the mobile app' 
      : 'WhatsApp will open in web browser'
  };
}
