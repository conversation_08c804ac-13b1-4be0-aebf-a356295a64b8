import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select } from "@/components/ui/select";
import { useUser } from "@/contexts/user-context";
import { useState, useEffect, useRef } from "react";

// Define types
interface Candidate {
  id: string;
  name: string;
  email: string;
  phone: string;
  jobTitle: string;
  status: "New" | "Contacted" | "Interviewing" | "Offer" | "Hired" | "Rejected";
  lastMessage?: string;
  lastMessageDate?: string;
  unread?: boolean;
}

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  senderType: "recruiter" | "candidate";
  content: string;
  timestamp: string;
  read: boolean;
}

export function RecruiterMessages() {
  const { userName } = useUser();
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [showComposeDialog, setShowComposeDialog] = useState(false);
  const [composeData, setComposeData] = useState({
    candidateId: "",
    subject: "",
    message: "",
  });
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Simulate loading data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      // In a real application, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock candidates data
      const mockCandidates: Candidate[] = [
        {
          id: "c1",
          name: "Chris Wilson",
          email: "<EMAIL>",
          phone: "+****************",
          jobTitle: "Senior Developer",
          status: "Interviewing",
          lastMessage: "Thanks for setting up the interview. Looking forward to it!",
          lastMessageDate: "2025-04-30 14:32",
          unread: false
        },
        {
          id: "c2",
          name: "Priya Sharma",
          email: "<EMAIL>",
          phone: "+****************",
          jobTitle: "UX Designer",
          status: "Contacted",
          lastMessage: "I'm available for an interview next Tuesday afternoon.",
          lastMessageDate: "2025-04-29 11:15",
          unread: true
        },
        {
          id: "c3",
          name: "Thomas Lee",
          email: "<EMAIL>",
          phone: "+****************",
          jobTitle: "Product Manager",
          status: "New",
          lastMessage: "Thanks for reaching out about the opportunity!",
          lastMessageDate: "2025-04-28 09:45",
          unread: false
        },
        {
          id: "c4",
          name: "Elena Rodriguez",
          email: "<EMAIL>",
          phone: "+****************",
          jobTitle: "Data Scientist",
          status: "Offer",
          lastMessage: "I've reviewed the offer and have a few questions.",
          lastMessageDate: "2025-04-27 16:20",
          unread: true
        },
        {
          id: "c5",
          name: "David Kim",
          email: "<EMAIL>",
          phone: "+****************",
          jobTitle: "DevOps Engineer",
          status: "Hired",
          lastMessage: "Got all the onboarding documents. Thanks!",
          lastMessageDate: "2025-04-25 13:10",
          unread: false
        }
      ];
      
      setCandidates(mockCandidates);
      setIsLoading(false);
    };
    
    loadData();
  }, []);

  // Load messages when a candidate is selected
  useEffect(() => {
    if (selectedCandidate) {
      setIsLoading(true);
      
      // In a real application, this would be an API call
      const loadMessages = async () => {
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Mock messages based on selected candidate
        const mockMessages: Message[] = [];
        const candidateId = selectedCandidate.id;
        const startDate = new Date(2025, 3, 25); // April 25, 2025
        
        // Generate different conversation patterns based on candidate status
        if (selectedCandidate.status === "New") {
          mockMessages.push({
            id: `${candidateId}-m1`,
            senderId: "recruiter1",
            senderName: userName,
            senderType: "recruiter",
            content: `Hi ${selectedCandidate.name}, I noticed your profile and think you might be a great fit for our ${selectedCandidate.jobTitle} position. Would you be interested in discussing this opportunity?`,
            timestamp: new Date(startDate.getTime()).toISOString(),
            read: true
          });
          
          mockMessages.push({
            id: `${candidateId}-m2`,
            senderId: candidateId,
            senderName: selectedCandidate.name,
            senderType: "candidate",
            content: "Thanks for reaching out about the opportunity! I'm definitely interested in learning more.",
            timestamp: new Date(startDate.getTime() + 3 * 60 * 60 * 1000).toISOString(),
            read: true
          });
        } else if (selectedCandidate.status === "Contacted") {
          mockMessages.push({
            id: `${candidateId}-m1`,
            senderId: "recruiter1",
            senderName: userName,
            senderType: "recruiter",
            content: `Hi ${selectedCandidate.name}, I noticed your profile and think you might be a great fit for our ${selectedCandidate.jobTitle} position. Would you be interested in discussing this opportunity?`,
            timestamp: new Date(startDate.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            read: true
          });
          
          mockMessages.push({
            id: `${candidateId}-m2`,
            senderId: candidateId,
            senderName: selectedCandidate.name,
            senderType: "candidate",
            content: "Thanks for reaching out! Yes, I'd be interested in learning more about the position.",
            timestamp: new Date(startDate.getTime() - 2 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(),
            read: true
          });
          
          mockMessages.push({
            id: `${candidateId}-m3`,
            senderId: "recruiter1",
            senderName: userName,
            senderType: "recruiter",
            content: "Great! Let's schedule a time to discuss the role. Are you available for an interview next week? We could do Tuesday or Thursday afternoon.",
            timestamp: new Date(startDate.getTime() - 24 * 60 * 60 * 1000).toISOString(),
            read: true
          });
          
          mockMessages.push({
            id: `${candidateId}-m4`,
            senderId: candidateId,
            senderName: selectedCandidate.name,
            senderType: "candidate",
            content: "I'm available for an interview next Tuesday afternoon.",
            timestamp: new Date(startDate.getTime() - 12 * 60 * 60 * 1000).toISOString(),
            read: selectedCandidate.unread ? false : true
          });
        } else if (selectedCandidate.status === "Interviewing") {
          mockMessages.push({
            id: `${candidateId}-m1`,
            senderId: "recruiter1",
            senderName: userName,
            senderType: "recruiter",
            content: `Hi ${selectedCandidate.name}, I've scheduled your technical interview for Wednesday at 2 PM with our senior engineering team. You'll be meeting with Alex Chen and Sophia Roberts. They'll be focusing on your technical skills and experience with similar projects.`,
            timestamp: new Date(startDate.getTime() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            read: true
          });
          
          mockMessages.push({
            id: `${candidateId}-m2`,
            senderId: candidateId,
            senderName: selectedCandidate.name,
            senderType: "candidate",
            content: "Thanks for setting up the interview. Looking forward to it! Should I prepare anything specific for the meeting?",
            timestamp: new Date(startDate.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            read: true
          });
          
          mockMessages.push({
            id: `${candidateId}-m3`,
            senderId: "recruiter1",
            senderName: userName,
            senderType: "recruiter",
            content: "It would be helpful to review our tech stack (React, Node.js, and MongoDB) and be prepared to discuss your experience with similar technologies. Also, be ready to walk through some of your past projects. Let me know if you have any other questions!",
            timestamp: new Date(startDate.getTime() - 2 * 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000).toISOString(),
            read: true
          });
        } else if (selectedCandidate.status === "Offer") {
          mockMessages.push({
            id: `${candidateId}-m1`,
            senderId: "recruiter1",
            senderName: userName,
            senderType: "recruiter",
            content: `Hi ${selectedCandidate.name}, I'm pleased to inform you that we'd like to extend an offer for the ${selectedCandidate.jobTitle} position! I've attached the formal offer letter with all the details. Please let me know if you have any questions.`,
            timestamp: new Date(startDate.getTime() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            read: true
          });
          
          mockMessages.push({
            id: `${candidateId}-m2`,
            senderId: candidateId,
            senderName: selectedCandidate.name,
            senderType: "candidate",
            content: "Thank you for the offer! I'm excited about the opportunity. I've reviewed the offer and have a few questions about the benefits package and remote work policy.",
            timestamp: new Date(startDate.getTime() - 24 * 60 * 60 * 1000).toISOString(),
            read: selectedCandidate.unread ? false : true
          });
        } else if (selectedCandidate.status === "Hired") {
          mockMessages.push({
            id: `${candidateId}-m1`,
            senderId: "recruiter1",
            senderName: userName,
            senderType: "recruiter",
            content: `Welcome to the team, ${selectedCandidate.name}! I've sent you an email with all the onboarding documents. Your start date is set for May 15th. Please complete the forms by the end of this week.`,
            timestamp: new Date(startDate.getTime() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            read: true
          });
          
          mockMessages.push({
            id: `${candidateId}-m2`,
            senderId: candidateId,
            senderName: selectedCandidate.name,
            senderType: "candidate",
            content: "Got all the onboarding documents. Thanks! I'll get them completed right away. Looking forward to starting!",
            timestamp: new Date(startDate.getTime() - 4 * 24 * 60 * 60 * 1000).toISOString(),
            read: true
          });
          
          mockMessages.push({
            id: `${candidateId}-m3`,
            senderId: "recruiter1",
            senderName: userName,
            senderType: "recruiter",
            content: "Perfect! Let me know if you have any questions. We're excited to have you join the team!",
            timestamp: new Date(startDate.getTime() - 4 * 24 * 60 * 60 * 1000 + 1 * 60 * 60 * 1000).toISOString(),
            read: true
          });
        }
        
        // Sort messages by timestamp
        setMessages(mockMessages.sort((a, b) => 
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        ));
        
        setIsLoading(false);
        
        // Mark candidate messages as read
        if (selectedCandidate.unread) {
          const updatedCandidates = candidates.map(c => 
            c.id === selectedCandidate.id ? { ...c, unread: false } : c
          );
          setCandidates(updatedCandidates);
        }
      };
      
      loadMessages();
    }
  }, [selectedCandidate, candidates, userName]);

  // Scroll to bottom of messages when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  const handleSendMessage = () => {
    if (!selectedCandidate || !newMessage.trim()) return;
    
    // Add new message to the conversation
    const newMessageObj: Message = {
      id: `new-${Date.now()}`,
      senderId: "recruiter1",
      senderName: userName,
      senderType: "recruiter",
      content: newMessage.trim(),
      timestamp: new Date().toISOString(),
      read: true
    };
    
    setMessages(prev => [...prev, newMessageObj]);
    
    // Update candidate with latest message
    const updatedCandidates = candidates.map(c => 
      c.id === selectedCandidate.id 
        ? { 
            ...c, 
            lastMessage: newMessage.trim(),
            lastMessageDate: new Date().toLocaleString('en-US', {
              year: 'numeric',
              month: 'numeric',
              day: 'numeric',
              hour: 'numeric',
              minute: 'numeric',
              hour12: true
            })
          } 
        : c
    );
    
    setCandidates(updatedCandidates);
    setNewMessage("");
  };

  const handleComposeSubmit = () => {
    if (!composeData.candidateId || !composeData.message.trim()) return;
    
    const candidate = candidates.find(c => c.id === composeData.candidateId);
    if (!candidate) return;
    
    // Create new message
    const newMessageObj: Message = {
      id: `new-${Date.now()}`,
      senderId: "recruiter1",
      senderName: userName,
      senderType: "recruiter",
      content: composeData.message.trim(),
      timestamp: new Date().toISOString(),
      read: true
    };
    
    // If this candidate is currently selected, add to current messages
    if (selectedCandidate && selectedCandidate.id === candidate.id) {
      setMessages(prev => [...prev, newMessageObj]);
    }
    
    // Update candidate with latest message
    const updatedCandidates = candidates.map(c => 
      c.id === candidate.id 
        ? { 
            ...c, 
            lastMessage: composeData.message.trim(),
            lastMessageDate: new Date().toLocaleString('en-US', {
              year: 'numeric',
              month: 'numeric',
              day: 'numeric',
              hour: 'numeric',
              minute: 'numeric',
              hour12: true
            })
          } 
        : c
    );
    
    setCandidates(updatedCandidates);
    
    // Reset and close dialog
    setComposeData({
      candidateId: "",
      subject: "",
      message: ""
    });
    setShowComposeDialog(false);
  };

  const formatMessageDate = (isoDate: string) => {
    const date = new Date(isoDate);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    });
  };

  const getStatusColor = (status: Candidate["status"]) => {
    switch (status) {
      case "New": return "bg-blue-100 text-blue-800";
      case "Contacted": return "bg-purple-100 text-purple-800";
      case "Interviewing": return "bg-amber-100 text-amber-800";
      case "Offer": return "bg-emerald-100 text-emerald-800";
      case "Hired": return "bg-green-100 text-green-800";
      case "Rejected": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="h-full flex flex-col space-y-0 p-1">
      <div className="bg-white rounded-lg shadow border border-gray-200 p-6 flex-1 flex flex-col h-full">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Candidate Messages</h1>
            <p className="text-gray-600">Communicate with candidates and manage your conversations</p>
          </div>
          
          <Button 
            onClick={() => setShowComposeDialog(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Compose Message
          </Button>
        </div>
        
        <div className="flex flex-1 border rounded-lg overflow-hidden h-full">
          {/* Candidates list */}
          <div className="w-80 border-r flex flex-col h-full">
            <div className="p-3 border-b">
              <Input
                placeholder="Search candidates..."
                className="w-full"
              />
            </div>
            
            <div className="flex-1 overflow-y-auto">
              {candidates.map(candidate => (
                <div
                  key={candidate.id}
                  className={`p-3 border-b cursor-pointer hover:bg-gray-50 ${
                    selectedCandidate?.id === candidate.id ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => setSelectedCandidate(candidate)}
                >
                  <div className="flex justify-between items-start">
                    <div className="font-medium text-gray-900 flex items-center">
                      {candidate.name}
                      {candidate.unread && (
                        <span className="ml-2 h-2 w-2 bg-blue-600 rounded-full"></span>
                      )}
                    </div>
                    <span className={`px-2 py-0.5 text-xs rounded-full ${getStatusColor(candidate.status)}`}>
                      {candidate.status}
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-500">{candidate.jobTitle}</div>
                  
                  {candidate.lastMessage && (
                    <div className="mt-1">
                      <div className="text-xs text-gray-400">{candidate.lastMessageDate}</div>
                      <div className="text-sm text-gray-600 truncate">{candidate.lastMessage}</div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
          
          {/* Chat area */}
          <div className="flex-1 flex flex-col h-full">
            {selectedCandidate ? (
              <>
                {/* Chat header */}
                <div className="p-4 border-b flex justify-between items-center">
                  <div>
                    <div className="font-medium text-gray-900">{selectedCandidate.name}</div>
                    <div className="text-sm text-gray-500">{selectedCandidate.email} • {selectedCandidate.phone}</div>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(selectedCandidate.status)}`}>
                    {selectedCandidate.status}
                  </span>
                </div>
                
                {/* Messages */}
                <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
                  {isLoading ? (
                    <div className="h-full flex items-center justify-center">
                      <div className="text-center">
                        <svg
                          className="animate-spin h-8 w-8 text-blue-600 mx-auto mb-4"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        <p className="text-gray-600">Loading messages...</p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {messages.map((message, index) => (
                        <motion.div
                          key={message.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.05 }}
                          className={`flex ${message.senderType === 'recruiter' ? 'justify-end' : 'justify-start'}`}
                        >
                          <div 
                            className={`max-w-[70%] rounded-lg p-3 ${
                              message.senderType === 'recruiter' 
                                ? 'bg-blue-600 text-white' 
                                : 'bg-white border border-gray-300 text-gray-800'
                            }`}
                          >
                            <div className="text-sm mb-1">
                              {message.content}
                            </div>
                            <div className={`text-xs ${
                              message.senderType === 'recruiter' 
                                ? 'text-blue-100' 
                                : 'text-gray-500'
                            }`}>
                              {formatMessageDate(message.timestamp)}
                            </div>
                          </div>
                        </motion.div>
                      ))}
                      <div ref={messagesEndRef} />
                    </div>
                  )}
                </div>
                
                {/* Message input */}
                <div className="p-4 border-t">
                  <div className="flex space-x-2">
                    <Input
                      placeholder="Type a message..."
                      value={newMessage}
                      onChange={e => setNewMessage(e.target.value)}
                      onKeyDown={e => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSendMessage();
                        }
                      }}
                      className="flex-1"
                    />
                    <Button 
                      onClick={handleSendMessage}
                      disabled={!newMessage.trim()}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      Send
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              <div className="h-full flex items-center justify-center text-center p-8">
                <div>
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                    ></path>
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No conversation selected</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Select a candidate from the list to view your conversation.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Compose Message Dialog */}
      <Dialog open={showComposeDialog} onOpenChange={setShowComposeDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Compose Message</DialogTitle>
          </DialogHeader>
          
          <div className="p-4 space-y-4">
            <div>
              <Label htmlFor="candidate">Recipient</Label>
              <Select value={composeData.candidateId} onValueChange={value => setComposeData(prev => ({ ...prev, candidateId: value }))} >
                <option value="" disabled>Select a candidate</option>
                {candidates.map(candidate => (
                  <option key={candidate.id} value={candidate.id}>
                    {candidate.name} - {candidate.jobTitle}
                  </option>
                ))}
              </Select>
            </div>
            
            <div>
              <Label htmlFor="subject">Subject (Optional)</Label>
              <Input
                id="subject"
                value={composeData.subject}
                onChange={e => setComposeData(prev => ({ ...prev, subject: e.target.value }))}
              />
            </div>
            
            <div>
              <Label htmlFor="message">Message</Label>
              <textarea
                id="message"
                value={composeData.message}
                onChange={e => setComposeData(prev => ({ ...prev, message: e.target.value }))}
                className="w-full h-32 px-3 py-2 text-gray-900 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div className="flex justify-end bg-gray-50 p-4 rounded-b-lg space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowComposeDialog(false)}
            >
              Cancel
            </Button>
            <Button
              className="bg-blue-600 hover:bg-blue-700"
              onClick={handleComposeSubmit}
              disabled={!composeData.candidateId || !composeData.message.trim()}
            >
              Send Message
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
