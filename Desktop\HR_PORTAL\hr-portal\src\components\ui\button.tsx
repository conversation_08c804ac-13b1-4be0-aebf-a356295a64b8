import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-[var(--ring)] focus-visible:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "bg-[var(--primary)] text-[var(--primary-foreground)] shadow-sm hover:bg-[var(--primary-hover)] hover:shadow-md active:scale-[0.98]",
        destructive:
          "bg-[var(--destructive)] text-[var(--destructive-foreground)] shadow-sm hover:bg-red-600 hover:shadow-md active:scale-[0.98]",
        outline:
          "border border-[var(--primary)] bg-white text-[var(--primary)] shadow-sm hover:bg-[var(--secondary)] hover:shadow-md active:scale-[0.98]",
        secondary:
          "bg-[var(--secondary)] text-[var(--secondary-foreground)] shadow-sm hover:bg-[var(--secondary-hover)] hover:shadow-md active:scale-[0.98]",
        ghost:
          "text-[var(--primary)] hover:bg-[var(--secondary)] hover:text-[var(--primary-dark)]",
        link: "text-[var(--primary)] underline-offset-4 hover:underline hover:text-[var(--primary-hover)]",
        success:
          "bg-[var(--success)] text-[var(--success-foreground)] shadow-sm hover:bg-emerald-600 hover:shadow-md active:scale-[0.98]",
        warning:
          "bg-[var(--warning)] text-[var(--warning-foreground)] shadow-sm hover:bg-amber-600 hover:shadow-md active:scale-[0.98]",
        info:
          "bg-[var(--info)] text-[var(--info-foreground)] shadow-sm hover:bg-cyan-600 hover:shadow-md active:scale-[0.98]",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : "button";

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

export { Button, buttonVariants };
