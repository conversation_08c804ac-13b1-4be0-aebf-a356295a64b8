// Define the Candidate type with required properties
export interface Candidate {
  id: number;
  jobId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  client: string;
  profile: string;
  skills: string;
  status: "New" | "Screening" | "Interview" | "Offer" | "Rejected" | "Hired";
  appliedDate: string;
  source: string;
  experience: number;
  education: string;
  location: string;
  salary: string;
  notes: string;
  lastUpdated: string;
  comment: string;
  peerReviewer: string;
  recruiter: string;
}

// Define column configuration
export interface Column {
  key: keyof Candidate;
  label: string;
  sortable: boolean;
}
