import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../index';
import { Job, FilterTag } from '../slices/jobsSlice';

// Base selectors
export const selectJobs = (state: RootState) => state.jobs.jobs;
export const selectJobsLoading = (state: RootState) => state.jobs.loading;
export const selectJobsError = (state: RootState) => state.jobs.error;
export const selectAppliedTags = (state: RootState) => state.jobs.appliedTags;
export const selectDateFilter = (state: RootState) => state.jobs.dateFilter;
export const selectAppliedCustomDateRange = (state: RootState) => state.jobs.appliedCustomDateRange;
export const selectCurrentPage = (state: RootState) => state.jobs.currentPage;
export const selectItemsPerPage = (state: RootState) => state.jobs.itemsPerPage;
export const selectSortConfig = (state: RootState) => state.jobs.sortConfig;
export const selectSearchTags = (state: RootState) => state.jobs.searchTags;

// Column definitions (moved from component)
const columns = [
  { key: "id", label: "Job ID", sortable: true },
  { key: "date_created", label: "Date Created", sortable: true },
  { key: "job_status", label: "Status", sortable: true },
  { key: "client", label: "Client", sortable: true },
  { key: "posted_by", label: "Posted By", sortable: true },
  { key: "recruiter", label: "Recruiter", sortable: true },
  { key: "role", label: "Role", sortable: true },
  { key: "no_of_positions", label: "Positions", sortable: true },
] as const;

// Memoized selector for filtered jobs
export const selectFilteredJobs = createSelector(
  [selectJobs, selectAppliedTags, selectDateFilter, selectAppliedCustomDateRange],
  (jobs, appliedTags, dateFilter, appliedCustomDateRange) => {
    const groupedTags = appliedTags.reduce((acc, tag) => {
      const key = tag.column;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(tag);
      return acc;
    }, {} as Record<string, FilterTag[]>);

    return jobs.filter((job) => {
      // Date Filter Logic
      const jobDate = new Date(job.date_created);
      if (isNaN(jobDate.getTime())) return false;
      
      if (dateFilter) {
        const now = new Date();
        const pastDate = new Date(now.setDate(now.getDate() - dateFilter));
        if (jobDate < pastDate) return false;
      }
      
      if (appliedCustomDateRange) {
        const startDate = new Date(appliedCustomDateRange.start);
        const endDate = new Date(appliedCustomDateRange.end);
        endDate.setDate(endDate.getDate() + 1);
        if (jobDate < startDate || jobDate >= endDate) return false;
      }

      // Tag-based Search Logic
      if (appliedTags.length > 0) {
        const matchesAllGroups = Object.values(groupedTags).every(tagGroup => {
          return tagGroup.some(tag => {
            const tagValue = tag.value.toLowerCase();
            const columnInfo = columns.find(c => c.label === tag.column);

            if (tag.column === 'Any') {
              return Object.values(job).some(val => String(val).toLowerCase().includes(tagValue));
            }
            if (!columnInfo) return false;

            const candidateValue = columnInfo.key === 'posted_by' ? job.management : job[columnInfo.key as keyof Job];
            return String(candidateValue).toLowerCase().includes(tagValue);
          });
        });

        if (!matchesAllGroups) {
          return false;
        }
      }

      return true;
    });
  }
);

// Memoized selector for sorted jobs
export const selectSortedJobs = createSelector(
  [selectFilteredJobs, selectSortConfig],
  (filteredJobs, sortConfig) => {
    return [...filteredJobs].sort((a, b) => {
      if (!sortConfig.key) return 0;
      
      let aValue: any;
      let bValue: any;
      
      if (sortConfig.key === "posted_by") {
        aValue = a.management;
        bValue = b.management;
      } else {
        aValue = a[sortConfig.key as keyof Job];
        bValue = b[sortConfig.key as keyof Job];
      }
      
      if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1;
      return 0;
    });
  }
);

// Memoized selector for paginated jobs
export const selectPaginatedJobs = createSelector(
  [selectSortedJobs, selectCurrentPage, selectItemsPerPage],
  (sortedJobs, currentPage, itemsPerPage) => {
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return sortedJobs.slice(indexOfFirstItem, indexOfLastItem);
  }
);

// Selector for search suggestions
export const selectSearchSuggestions = createSelector(
  [selectFilteredJobs, (state: RootState, inputValue: string) => inputValue],
  (filteredJobs, inputValue) => {
    if (!inputValue) return [];
    
    const suggestions: { value: string; column: string }[] = [];
    filteredJobs.forEach(job => {
      columns.forEach(col => {
        if (!col.key) return;
        const value = col.key === 'posted_by' ? job.management : job[col.key as keyof Job];
        if (typeof value === 'string' && value.toLowerCase().includes(inputValue.toLowerCase())) {
          if (!suggestions.some(s => s.value === value && s.column === col.label)) {
            suggestions.push({ value, column: col.label });
          }
        }
      });
    });
    return suggestions.slice(0, 7);
  }
);

// Export columns for use in components
export { columns };
