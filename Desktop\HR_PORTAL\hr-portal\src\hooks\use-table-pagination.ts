import { useState } from 'react';
import { useAnimation } from 'framer-motion';

interface UsePaginationOptions {
  /**
   * Initial page
   */
  initialPage?: number;
  
  /**
   * Initial items per page
   */
  initialItemsPerPage?: number;
  
  /**
   * Available page size options
   */
  pageSizeOptions?: number[];
}

/**
 * Hook for handling table pagination with animations
 */
export function useTablePagination({
  initialPage = 1,
  initialItemsPerPage = 10,
  pageSizeOptions = [5, 10, 20, 50],
}: UsePaginationOptions = {}) {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [itemsPerPage, setItemsPerPage] = useState(initialItemsPerPage);
  
  // Animation controls for table
  const tableControls = useAnimation();
  
  // Function to handle page change with animation
  const handlePageChange = async (newPage: number) => {
    // Apply a fade transition when changing pages
    await tableControls.start({
      opacity: 0.3,
      y: 10,
      transition: { duration: 0.2 }
    });
    
    setCurrentPage(newPage);
    
    // Animate back in
    tableControls.start({
      opacity: 1,
      y: 0,
      transition: { duration: 0.3 }
    });
  };
  
  // Function to handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page
  };
  
  // Function to paginate data
  const paginateData = <T>(data: T[]) => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return data.slice(startIndex, startIndex + itemsPerPage);
  };
  
  // Calculate total pages
  const getTotalPages = (totalItems: number) => {
    return Math.ceil(totalItems / itemsPerPage);
  };
  
  return {
    currentPage,
    itemsPerPage,
    pageSizeOptions,
    tableControls,
    handlePageChange,
    handleItemsPerPageChange,
    paginateData,
    getTotalPages,
    setCurrentPage,
    setItemsPerPage,
  };
}
