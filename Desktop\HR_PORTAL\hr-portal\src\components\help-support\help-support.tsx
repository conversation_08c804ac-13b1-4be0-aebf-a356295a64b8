import { useState } from "react";
import { Upload, <PERSON>, <PERSON>O<PERSON>, Edit, Trash2, MessageSquare } from "lucide-react";
import { KebabMenu, createKebabMenuItem, type KebabMenuItem } from "@/components/ui/kebab-menu";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";

// Types for the form
interface IssueFormData {
  title: string;
  type: string;
  severity: string;
  module: string;
  description: string;
  files: File[];
}

// Types for historical issues
interface HistoricalIssue {
  id: number;
  createdOn: string;
  lastUpdated: string;
  raisedBy: string;
  issueTitle: string;
  issueDescription: string;
  issueType: string;
  severity: string;
  status: string;
  module: string;
  assignedTo: string;
  resolutionNotes: string;
}

// Mock data for historical issues
const mockHistoricalIssues: HistoricalIssue[] = [
  {
    id: 33,
    createdOn: "31/07/2025",
    lastUpdated: "01/01/1970",
    raisedBy: "Leonard Jereid Lambert",
    issueTitle: "Unable to edit the role in the JOB Assignments (after assigning)",
    issueDescription: "I have assigned a job but unable to edit the role in the JOB",
    issueType: "Other",
    severity: "Medium",
    status: "Open",
    module: "Job Assignments",
    assignedTo: "ATS Support Team",
    resolutionNotes: "-",
  },
  {
    id: 51,
    createdOn: "31/07/2025",
    lastUpdated: "01/01/1970",
    raisedBy: "ManagerOne",
    issueTitle: "testing purpose",
    issueDescription: "testing purpose please ignore it",
    issueType: "Other",
    severity: "Low",
    status: "Open",
    module: "Other",
    assignedTo: "ATS Support Team",
    resolutionNotes: "-",
  },
  {
    id: 51,
    createdOn: "31/07/2025",
    lastUpdated: "01/01/1970",
    raisedBy: "ManagerOne",
    issueTitle: "dsgfdg",
    issueDescription: "sergasgadrg",
    issueType: "Performance",
    severity: "High",
    status: "Open",
    module: "Dashboard",
    assignedTo: "ATS Support Team",
    resolutionNotes: "-",
  },
];

export function HelpSupport() {
  const [formData, setFormData] = useState<IssueFormData>({
    title: "",
    type: "",
    severity: "",
    module: "",
    description: "",
    files: [],
  });

  const [showHistoricalIssues, setShowHistoricalIssues] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  // Handle form input changes
  const handleInputChange = (field: keyof IssueFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Handle file upload
  const handleFiles = (files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files);
      setFormData(prev => ({ ...prev, files: [...prev.files, ...fileArray] }));
    }
  };

  // Handle drag events
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Issue submitted:", formData);
    alert("Issue raised successfully!");
    
    // Reset form
    setFormData({
      title: "",
      type: "",
      severity: "",
      module: "",
      description: "",
      files: [],
    });
  };

  // Remove file
  const removeFile = (index: number) => {
    setFormData(prev => ({
      ...prev,
      files: prev.files.filter((_, i) => i !== index)
    }));
  };

  // Create kebab menu items for each historical issue
  const createIssueMenuItems = (issue: HistoricalIssue): KebabMenuItem[] => [
    createKebabMenuItem(
      "view-details",
      "View Details",
      () => alert(`Viewing details for issue #${issue.id}`),
      { icon: Eye }
    ),
    createKebabMenuItem(
      "add-comment",
      "Add Comment",
      () => alert(`Adding comment to issue #${issue.id}`),
      { icon: MessageSquare }
    ),
    createKebabMenuItem(
      "edit-issue",
      "Edit Issue",
      () => alert(`Editing issue #${issue.id}`),
      { icon: Edit, separator: true }
    ),
    createKebabMenuItem(
      "delete-issue",
      "Delete Issue",
      () => {
        if (window.confirm(`Are you sure you want to delete issue #${issue.id}?`)) {
          alert(`Deleting issue #${issue.id}`);
        }
      },
      { icon: Trash2, variant: "destructive" }
    ),
  ];

  return (
    <div className="h-full w-full flex flex-col">
      <div className="bg-white p-6 flex-1 w-full">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-[var(--primary-dark)]">Help & Support</h1>
            <Button
              variant="outline"
              onClick={() => setShowHistoricalIssues(!showHistoricalIssues)}
              className="flex items-center gap-2"
            >
              {showHistoricalIssues ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {showHistoricalIssues ? "Hide" : "View"} Previous Issues
            </Button>
          </div>

          {/* Historical Issues Table */}
          {showHistoricalIssues && (
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-[var(--primary-dark)] mb-4">Historical Issues</h2>
              <AnimatedTableWrapper className="border border-gray-200 rounded-md overflow-hidden">
                <div className="overflow-x-auto overflow-y-auto h-[400px]">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">ID</th>
                        <th className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">Created On</th>
                        <th className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">Last Updated</th>
                        <th className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">Raised By</th>
                        <th className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">Issue Title</th>
                        <th className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">Issue Description</th>
                        <th className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">Issue Type</th>
                        <th className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">Severity</th>
                        <th className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">Status</th>
                        <th className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">Module</th>
                        <th className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">Assigned To</th>
                        <th className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">Resolution Notes</th>
                        <th className="sticky top-0 z-10 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-16">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {mockHistoricalIssues.map((issue, index) => (
                        <AnimatedTableRow key={issue.id} index={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{issue.id}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{issue.createdOn}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{issue.lastUpdated}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{issue.raisedBy}</td>
                          <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">{issue.issueTitle}</td>
                          <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">{issue.issueDescription}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              issue.issueType === "Other" ? "bg-gray-100 text-gray-800" :
                              issue.issueType === "Performance" ? "bg-red-100 text-red-800" :
                              "bg-blue-100 text-blue-800"
                            }`}>
                              {issue.issueType}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              issue.severity === "Low" ? "bg-green-100 text-green-800" :
                              issue.severity === "Medium" ? "bg-yellow-100 text-yellow-800" :
                              issue.severity === "High" ? "bg-red-100 text-red-800" :
                              "bg-purple-100 text-purple-800"
                            }`}>
                              {issue.severity}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              {issue.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{issue.module}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{issue.assignedTo}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{issue.resolutionNotes}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                            <KebabMenu items={createIssueMenuItems(issue)} />
                          </td>
                        </AnimatedTableRow>
                      ))}
                    </tbody>
                  </table>
                </div>
              </AnimatedTableWrapper>
            </div>
          )}

          {/* Issue Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* File Upload Area */}
            <div className="mb-6">
              <div
                className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive
                    ? "border-blue-400 bg-blue-50"
                    : "border-gray-300 hover:border-gray-400"
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <div className="text-lg font-medium text-gray-900 mb-2">Drop files here</div>
                <div className="text-sm text-gray-500 mb-4">Drag files here • Paste images with Ctrl+V</div>
                
                <input
                  type="file"
                  multiple
                  onChange={(e) => handleFiles(e.target.files)}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </div>
              
              {/* Display uploaded files */}
              {formData.files.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Uploaded Files:</h4>
                  <div className="space-y-2">
                    {formData.files.map((file, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                        <span className="text-sm text-gray-700">{file.name}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                          className="text-red-600 hover:text-red-800"
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Form Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Issue Title */}
              <div>
                <Label htmlFor="title" className="text-sm font-medium text-gray-700">
                  Issue Title <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="title"
                  type="text"
                  placeholder="Enter a brief title for the issue"
                  value={formData.title}
                  onChange={(e) => handleInputChange("title", e.target.value)}
                  required
                  className="mt-1"
                />
              </div>

              {/* Issue Type */}
              <div>
                <Label htmlFor="type" className="text-sm font-medium text-gray-700">
                  Issue Type <span className="text-red-500">*</span>
                </Label>
                <Select value={formData.type} onValueChange={(value) => handleInputChange("type", value)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select Issue Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Bug">Bug</SelectItem>
                    <SelectItem value="Enhancement">Enhancement</SelectItem>
                    <SelectItem value="Feature Request">Feature Request</SelectItem>
                    <SelectItem value="Data Error">Data Error</SelectItem>
                    <SelectItem value="Performance">Performance</SelectItem>
                    <SelectItem value="UI/UX">UI/UX</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Severity */}
              <div>
                <Label htmlFor="severity" className="text-sm font-medium text-gray-700">
                  Severity <span className="text-red-500">*</span>
                </Label>
                <Select value={formData.severity} onValueChange={(value) => handleInputChange("severity", value)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select Severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Low">Low</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="High">High</SelectItem>
                    <SelectItem value="Critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Module */}
              <div>
                <Label htmlFor="module" className="text-sm font-medium text-gray-700">
                  Module <span className="text-red-500">*</span>
                </Label>
                <Select value={formData.module} onValueChange={(value) => handleInputChange("module", value)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select Module" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Analytics">Analytics</SelectItem>
                    <SelectItem value="Calendar">Calendar</SelectItem>
                    <SelectItem value="Change Password">Change Password</SelectItem>
                    <SelectItem value="Dashboard">Dashboard</SelectItem>
                    <SelectItem value="Job Assignments">Job Assignments</SelectItem>
                    <SelectItem value="Job Listing">Job Listing</SelectItem>
                    <SelectItem value="Peer Assigned Profiles">Peer Assigned Profiles</SelectItem>
                    <SelectItem value="Profile Transfer">Profile Transfer</SelectItem>
                    <SelectItem value="Register Candidate">Register Candidate</SelectItem>
                    <SelectItem value="User Accounts">User Accounts</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Issue Description */}
            <div>
              <Label htmlFor="description" className="text-sm font-medium text-gray-700">
                Issue Description <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="description"
                placeholder="Describe the issue in detail... Include steps to reproduce, expected behavior, and actual behavior."
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                required
                className="mt-1 min-h-[120px]"
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button
                type="submit"
                variant="success"
                className="px-8 py-2 text-lg font-medium"
              >
                RAISE ISSUE
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
