import React, { ReactNode } from "react";
import { motion } from "framer-motion";

interface AnimatedTableRowProps {
  /**
   * The row content
   */
  children: ReactNode;
  
  /**
   * Index of the row for staggered animations
   */
  index?: number;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Any additional props to pass to the motion.tr element
   */
  [key: string]: any;
}

/**
 * A component that adds Framer Motion animations to table rows
 */
export function AnimatedTableRow({
  children,
  index = 0,
  className = "",
  ...props
}: AnimatedTableRowProps) {
  return (
    <motion.tr
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{
        duration: 0.3,
        delay: index * 0.05, // Staggered animation
        type: "spring",
        stiffness: 100,
        damping: 15,
      }}
      whileHover={{
        backgroundColor: "#f9fafb",
        transition: { duration: 0.1 },
      }}
      className={`hover:bg-gray-50 ${className}`}
      {...props}
    >
      {children}
    </motion.tr>
  );
}
