import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { ApiService, convertApiCandidateToLocal, type ApiResponse } from '@/services/api';
import { type Candidate } from '@/types/candidate';
import { useUser } from './user-context';

interface CandidatesContextType {
  candidates: Candidate[];
  loading: boolean;
  error: string | null;
  lastFetched: Date | null;
  apiResponse: ApiResponse | null;
  fetchCandidates: (force?: boolean) => Promise<void>;
  refreshCandidates: () => Promise<void>;
  clearCache: () => void;
}

const CandidatesContext = createContext<CandidatesContextType>({
  candidates: [],
  loading: false,
  error: null,
  lastFetched: null,
  apiResponse: null,
  fetchCandidates: async () => {},
  refreshCandidates: async () => {},
  clearCache: () => {},
});

export const useCandidates = () => useContext(CandidatesContext);

interface CandidatesProviderProps {
  children: React.ReactNode;
}

// Cache duration in milliseconds (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000;

export const CandidatesProvider: React.FC<CandidatesProviderProps> = ({ children }) => {
  const { userEmail, userRole, userName, userId } = useUser();
  
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastFetched, setLastFetched] = useState<Date | null>(null);
  const [apiResponse, setApiResponse] = useState<ApiResponse | null>(null);

  // Check if cache is still valid
  const isCacheValid = useCallback(() => {
    if (!lastFetched) return false;
    const now = new Date();
    const timeDiff = now.getTime() - lastFetched.getTime();
    return timeDiff < CACHE_DURATION;
  }, [lastFetched]);

  // Fetch candidates from API
  const fetchCandidates = useCallback(async (force = false) => {
    // Don't fetch if user info is not available
    if (!userEmail || !userRole || !userName || !userId) {
      return;
    }

    // Don't fetch if cache is valid and not forced
    if (!force && isCacheValid() && candidates.length > 0) {
      console.log('Using cached candidates data');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('Fetching candidates from API...');

      // Use the actual userId from authentication
      const userType = userRole === "manager" ? "management" : "recruiter";

      const response = await ApiService.fetchCandidates(
        userId,
        userType,
        userName,
        1 // For now, always fetch page 1
      );

      setApiResponse(response);
      
      // Convert API candidates to local format
      const convertedCandidates = response.candidates.map(convertApiCandidateToLocal);
      setCandidates(convertedCandidates);
      setLastFetched(new Date());
      
      console.log(`Fetched ${convertedCandidates.length} candidates and cached them`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch candidates';
      setError(errorMessage);
      console.error('Failed to fetch candidates:', err);
    } finally {
      setLoading(false);
    }
  }, [userEmail, userRole, userName, userId, isCacheValid, candidates.length]);

  // Force refresh candidates (ignores cache)
  const refreshCandidates = useCallback(async () => {
    console.log('Force refreshing candidates...');
    await fetchCandidates(true);
  }, [fetchCandidates]);

  // Clear cache
  const clearCache = useCallback(() => {
    console.log('Clearing candidates cache');
    setCandidates([]);
    setLastFetched(null);
    setApiResponse(null);
    setError(null);
  }, []);

  // Auto-fetch on mount and when user changes
  useEffect(() => {
    if (userEmail && userRole && userName) {
      fetchCandidates();
    }
  }, [userEmail, userRole, userName, fetchCandidates]);

  // Clear cache when user logs out
  useEffect(() => {
    if (!userEmail) {
      clearCache();
    }
  }, [userEmail, clearCache]);

  const value = {
    candidates,
    loading,
    error,
    lastFetched,
    apiResponse,
    fetchCandidates,
    refreshCandidates,
    clearCache,
  };

  return (
    <CandidatesContext.Provider value={value}>
      {children}
    </CandidatesContext.Provider>
  );
};

// Hook to get cache status
export const useCandidatesCacheStatus = () => {
  const { lastFetched, candidates } = useCandidates();
  
  const isCacheValid = useCallback(() => {
    if (!lastFetched) return false;
    const now = new Date();
    const timeDiff = now.getTime() - lastFetched.getTime();
    return timeDiff < CACHE_DURATION;
  }, [lastFetched]);

  return {
    hasCache: candidates.length > 0,
    isCacheValid: isCacheValid(),
    lastFetched,
    cacheAge: lastFetched ? new Date().getTime() - lastFetched.getTime() : null,
  };
};
