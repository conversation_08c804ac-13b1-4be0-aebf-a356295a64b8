import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>er,
  Navigate,
  RouteObject,
  useNavigate,
} from "react-router-dom";
import { Suspense, lazy } from "react";
import { useEffect } from "react";
import { UserRole } from "./contexts/user-context";
import { MainLayout } from "./components/layout/main-layout";
import PeerAssignedProfiles from "./components/peer-assigned-profiles";

// Implement lazy loading for all components
const Dashboard = lazy(() => import("./components/dashboard/dashboard").then(module => ({ default: module.Dashboard })));
const Login = lazy(() => import("./components/auth/login").then(module => ({ default: module.Login })));
const Register = lazy(() => import("./components/auth/register").then(module => ({ default: module.Register })));
const Logout = lazy(() => import("./components/auth/logout").then(module => ({ default: module.Logout })));
const ChangePassword = lazy(() => import("./components/auth/change-password").then(module => ({ default: module.ChangePassword })));
const AssignedRequirements = lazy(() => import("./components/requirements/assigned-requirements").then(module => ({ default: module.AssignedRequirements })));
const RegisterCandidate = lazy(() => import("./components/candidates/register-candidate").then(module => ({ default: module.RegisterCandidate })));
const CalendarView = lazy(() => import("./components/calendar/calendar"));
const JobListing = lazy(() => import("./components/jobs/job-listing").then(module => ({ default: module.JobListing })));
const JobAssignments = lazy(() => import("./components/jobs/job-assignments").then(module => ({ default: module.JobAssignments })));
const UserAccounts = lazy(() => import("./components/users/user-accounts").then(module => ({ default: module.UserAccounts })));
const ProfileTransfer = lazy(() => import("./components/profile/profile-transfer").then(module => ({ default: module.ProfileTransfer })));
const ManagerAnalytics = lazy(() => import("./components/analytics/manager-analytics").then(module => ({ default: module.ManagerAnalytics })));
const RecruiterAnalytics = lazy(() => import("./components/analytics/recruiter-analytics").then(module => ({ default: module.RecruiterAnalytics })));
const RecruiterMessages = lazy(() => import("./components/messages/recruiter-messages").then(module => ({ default: module.RecruiterMessages })));
const ProfileAnalysis = lazy(() => import("./components/profile-analysis/profile-analysis").then(module => ({ default: module.ProfileAnalysis })));
const HelpSupport = lazy(() => import("./components/help-support/help-support").then(module => ({ default: module.HelpSupport })));

// Loading spinner component for suspense fallback
const LoadingSpinner = () => {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center">
        <svg
          className="animate-spin h-10 w-10 text-blue-600 mx-auto mb-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        <p className="text-gray-600">Loading...</p>
      </div>
    </div>
  );
};

// Component to redirect based on user role
const RoleBasedRedirect = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const userRole = localStorage.getItem("userRole") as UserRole;

    // Redirect based on role
    if (userRole === "manager") {
      navigate("/manager/dashboard", { replace: true });
    } else if (userRole === "recruiter") {
      navigate("/recruiter/dashboard", { replace: true });
    } else {
      // Fallback to login if no role is found
      navigate("/login", { replace: true });
    }
  }, [navigate]);

  // Return null as this is just a redirect component
  return null;
};

// Auth guard component
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
}

const ProtectedRoute = ({ children, requiredRoles }: ProtectedRouteProps) => {
  const isAuthenticated = localStorage.getItem("isAuthenticated") === "true";
  const userRole = localStorage.getItem("userRole") as UserRole;

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRoles && !requiredRoles.includes(userRole)) {
    return <Navigate to={`/${userRole}/dashboard`} replace />;
  }

  return <>{children}</>;
};

// Layout wrapper for authenticated routes
const AuthenticatedLayout = ({
  children,
  requiredRoles,
}: {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
}) => {
  return (
    <ProtectedRoute requiredRoles={requiredRoles}>
      <MainLayout>{children}</MainLayout>
    </ProtectedRoute>
  );
};

// Define routes
const routes: RouteObject[] = [
  // Public routes
  {
    path: "/login",
    element: <Suspense fallback={<LoadingSpinner />}><Login /></Suspense>,
  },
  {
    path: "/register",
    element: <Suspense fallback={<LoadingSpinner />}><Register /></Suspense>,
  },
  {
    path: "/logout",
    element: <Suspense fallback={<LoadingSpinner />}><Logout /></Suspense>,
  },

  // Root redirect based on user role
  {
    path: "/",
    element: (
      <ProtectedRoute>
        <Suspense fallback={<LoadingSpinner />}>
          <RoleBasedRedirect />
        </Suspense>
      </ProtectedRoute>
    ),
  },

  // Common routes - Dashboard redirects to role-specific dashboard
  {
    path: "/dashboard",
    element: (
      <ProtectedRoute>
        <Suspense fallback={<LoadingSpinner />}>
          <RoleBasedRedirect />
        </Suspense>
      </ProtectedRoute>
    ),
  },
  {
    path: "/change-password",
    element: (
      <AuthenticatedLayout>
        <Suspense fallback={<LoadingSpinner />}>
          <ChangePassword />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/calendar",
    element: (
      <AuthenticatedLayout>
        <Suspense fallback={<LoadingSpinner />}>
          <CalendarView />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/register-candidate",
    element: (
      <AuthenticatedLayout>
        <Suspense fallback={<LoadingSpinner />}>
          <RegisterCandidate />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/help-and-support",
    element: (
      <AuthenticatedLayout>
        <Suspense fallback={<LoadingSpinner />}>
          <HelpSupport />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },

  // Manager routes
  {
    path: "/manager/dashboard",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <Dashboard />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/job-listing",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <JobListing />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/job-assignments",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <JobAssignments />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/register-candidate",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RegisterCandidate />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/profile-transfer",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <ProfileTransfer />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/analytics",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <ManagerAnalytics />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/user-accounts",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <UserAccounts />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/peer-assigned-profiles",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <PeerAssignedProfiles />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },

  // Recruiter routes
  {
    path: "/recruiter/dashboard",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <Dashboard />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/requirements",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <AssignedRequirements />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/register-candidate",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RegisterCandidate />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/messages",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RecruiterMessages />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/analytics",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RecruiterAnalytics />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/profile-analysis",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <ProfileAnalysis />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/peer-assigned-profiles",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <PeerAssignedProfiles />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },

  // Catch-all route
  {
    path: "*",
    element: <Navigate to="/dashboard" replace />,
  },
];

// Create router
export const router = createBrowserRouter(routes);
