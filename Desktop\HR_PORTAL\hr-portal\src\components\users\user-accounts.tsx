import { useState, useEffect, useRef } from "react";
import {
  CheckCircle,
  UserX,
  Trash2,
  Plus,
  User as UserIcon,
  Download,
} from "lucide-react";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { AdvancedSearchBar } from "@/components/ui/advanced-search-bar";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchUsers,
  addSearchTag,
  removeSearchTag,
  applyFilters,
  clearAllFilters,
  setCurrentPage,
  setItemsPerPage,
  setSortConfig,
  toggleRowSelection,
  setSelectAll,
  clearSelection,
  toggleColumnVisibility,
  type FilterTag,
  type User,
} from "@/store/slices/userAccountsSlice";
import {
  selectUsersLoading,
  selectUsersError,
  selectSearchTags,
  selectAppliedTags,
  selectCurrentPage,
  selectItemsPerPage,
  selectSortConfig,
  selectSelectedRows,
  selectSelectAll,
  selectVisibleColumns,
  selectFilteredUsers,
  selectPaginatedUsers,
  selectFilteredColumns,
  selectSearchSuggestions,
  allUserColumns,
} from "@/store/selectors/userAccountsSelectors";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { type ActiveUser } from "@/services/api";

// Types and conversion logic are now in Redux slice

export function UserAccounts() {
  return (
    <div className="h-full w-full flex flex-col">
      <div className="bg-white p-6 flex-1 w-full">
        <UserAccountsTable />
      </div>
    </div>
  );
}

// Interface for new user form
interface NewUserFormData {
  username: string;
  name: string;
  email: string;
  userType: "Manager" | "Recruiter" | "";
}

function UserAccountsTable() {
  const dispatch = useAppDispatch();

  // Redux state
  const loading = useAppSelector(selectUsersLoading);
  const error = useAppSelector(selectUsersError);
  const searchTags = useAppSelector(selectSearchTags);
  const appliedTags = useAppSelector(selectAppliedTags);
  const currentPage = useAppSelector(selectCurrentPage);
  const itemsPerPage = useAppSelector(selectItemsPerPage);
  const sortConfig = useAppSelector(selectSortConfig);
  const selectedRows = useAppSelector(selectSelectedRows);
  const selectAll = useAppSelector(selectSelectAll);
  const visibleColumns = useAppSelector(selectVisibleColumns);
  const currentUsers = useAppSelector(selectPaginatedUsers);

  // Local UI state
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newUserData, setNewUserData] = useState<NewUserFormData>({
    username: "",
    name: "",
    email: "",
    userType: "",
  });

  // Animation controls
  const { isLoading, animateSorting, animatePagination } = useTableAnimation();

  // Get search suggestions from Redux
  const suggestions = useAppSelector(selectSearchSuggestions);

  // Close columns dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        columnsDropdownRef.current &&
        !columnsDropdownRef.current.contains(event.target as Node) &&
        columnsButtonRef.current &&
        !columnsButtonRef.current.contains(event.target as Node)
      ) {
        setIsColumnsDropdownOpen(false);
      }
    }

    if (isColumnsDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isColumnsDropdownOpen]);

  // Toggle column visibility
  const toggleColumnVisibility = (columnKey: string) => {
    setVisibleColumns(prev => ({
      ...prev,
      [columnKey]: !prev[columnKey]
    }));
  };

  // Get filtered columns based on visibility
  const filteredColumns = columns.filter(column => visibleColumns[String(column.key)]);

  // Selection helper functions
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      const allIds = new Set(currentUsers.map(user => user.id));
      setSelectedRows(allIds);
    } else {
      setSelectedRows(new Set());
    }
  };

  const handleSelectRow = (userId: number, checked: boolean) => {
    const newSelectedRows = new Set(selectedRows);
    if (checked) {
      newSelectedRows.add(userId);
    } else {
      newSelectedRows.delete(userId);
    }
    setSelectedRows(newSelectedRows);
    setSelectAll(newSelectedRows.size === currentUsers.length);
  };

  // Download selected data
  const handleDownloadSelected = () => {
    const selectedUsers = currentUsers.filter(user =>
      selectedRows.has(user.id)
    );

    if (selectedUsers.length === 0) {
      alert('Please select at least one row to download.');
      return;
    }

    // Convert to CSV format
    const headers = filteredColumns.map(col => col.label).join(',');
    const csvData = selectedUsers.map(user =>
      filteredColumns.map(col => {
        let value = user[col.key];
        if (col.key === 'firstName') {
          value = `${user.firstName} ${user.lastName}`;
        }
        // Escape commas and quotes in CSV
        return typeof value === 'string' && (value.includes(',') || value.includes('"'))
          ? `"${value.replace(/"/g, '""')}"`
          : String(value);
      }).join(',')
    ).join('\n');

    const csvContent = `${headers}\n${csvData}`;

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `users_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Fetch users from API
  useEffect(() => {
    dispatch(fetchUsers());
  }, [dispatch]);

  // Filtering, sorting, and pagination are now handled by Redux selectors

  // Change page with animation
  const paginate = async (pageNumber: number) => {
    await animatePagination();
    dispatch(setCurrentPage(pageNumber));
  };

  // Handle sort with animation
  const handleSort = async (key: keyof User) => {
    await animateSorting();

    let direction: "ascending" | "descending" | null = "ascending";

    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    } else if (
      sortConfig.key === key &&
      sortConfig.direction === "descending"
    ) {
      direction = null;
    }

    dispatch(setSortConfig({ key, direction }));
  };

  // Handle input change for new user form
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewUserData({
      ...newUserData,
      [name]: value,
    });
  };

  // Handle user type selection
  const handleUserTypeChange = (value: string) => {
    setNewUserData({
      ...newUserData,
      userType: value as "Manager" | "Recruiter" | "",
    });
  };

  // Handle add new user
  const handleAddNewUser = () => {
    setIsDialogOpen(true);
  };

  // Handle form submission
  const handleSubmitNewUser = () => {
    // Validate form
    if (
      !newUserData.username ||
      !newUserData.name ||
      !newUserData.email ||
      !newUserData.userType
    ) {
      alert("Please fill in all fields");
      return;
    }

    // In a real app, you would send this data to an API
    console.log("Creating new user:", newUserData);

    // For demo purposes, show success message
    alert(`New user ${newUserData.name} created successfully!`);

    // Reset form and close dialog
    setNewUserData({
      username: "",
      name: "",
      email: "",
      userType: "",
    });
    setIsDialogOpen(false);
  };

  // Handle toggle actions
  const handleToggleVerification = async (user: User, newStatus: boolean) => {
    try {
      await ApiService.updateUserStatus(user.username, newStatus, 'verify');
      // Update local state
      setUsers(prevUsers =>
        prevUsers.map(u =>
          u.id === user.id ? { ...u, isVerified: newStatus } : u
        )
      );
    } catch (error) {
      console.error('Failed to update verification status:', error);
      alert('Failed to update verification status');
    }
  };

  const handleToggleActivation = async (user: User, newStatus: boolean) => {
    try {
      await ApiService.updateUserStatus(user.username, newStatus, 'deactivate');
      // Update local state
      setUsers(prevUsers =>
        prevUsers.map(u =>
          u.id === user.id ? { ...u, isActive: newStatus } : u
        )
      );
    } catch (error) {
      console.error('Failed to update activation status:', error);
      alert('Failed to update activation status');
    }
  };

  const handleTogglePeerReviewer = async (user: User, newStatus: boolean) => {
    try {
      await ApiService.updateUserStatus(user.username, newStatus, 'peer_reviewer');
      // Update local state
      setUsers(prevUsers =>
        prevUsers.map(u =>
          u.id === user.id ? { ...u, isPeerReviewer: newStatus } : u
        )
      );
    } catch (error) {
      console.error('Failed to update peer reviewer status:', error);
      alert('Failed to update peer reviewer status');
    }
  };

  const handleDeleteUser = (userId: number) => {
    alert(`Delete user ${userId}`);
  };

  return (
    <div className="flex flex-col">
      {/* Advanced Search Bar */}
      <AdvancedSearchBar
        searchTags={searchTags}
        onAddTag={(tag) => dispatch(addSearchTag(tag))}
        onRemoveTag={(tag) => dispatch(removeSearchTag(tag))}
        onApplyFilters={() => dispatch(applyFilters())}
        onClearAllFilters={() => dispatch(clearAllFilters())}
        suggestions={suggestions}
        placeholder="Search users..."
        showDateFilter={false}
        showColumnToggle={true}
        columns={allUserColumns}
        visibleColumns={visibleColumns}
        onToggleColumn={(columnKey) => dispatch(toggleColumnVisibility(columnKey as keyof User))}
      />

      {/* Items per page selector */}
      <div className="mb-4 flex justify-end">
        <select
          className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
          onChange={async (e) => {
            await animatePagination();
            dispatch(setItemsPerPage(Number(e.target.value)));
          }}
          value={itemsPerPage}
        >
          <option value="10">10 per page</option>
          <option value="20">20 per page</option>
          <option value="50">50 per page</option>
          <option value="100">100 per page</option>
        </select>
      </div>


          {/* Columns Toggle Button */}
          <div className="relative">
            <button
              ref={columnsButtonRef}
              onClick={() => setIsColumnsDropdownOpen(!isColumnsDropdownOpen)}
              className="bg-white border border-gray-300 text-gray-900 px-4 py-2.5 rounded-md text-sm font-medium flex items-center gap-2 hover:bg-gray-50 focus:ring-blue-500 focus:border-blue-500"
            >
              <Columns className="h-3.5 w-3.5" />
              Columns
              <ChevronDown className={`h-3.5 w-3.5 transition-transform ${isColumnsDropdownOpen ? 'rotate-180' : ''}`} />
            </button>

            {/* Columns Dropdown */}
            {isColumnsDropdownOpen && (
              <div
                ref={columnsDropdownRef}
                className="absolute right-0 top-full mt-1 z-[9999] bg-white rounded-lg shadow-xl border border-gray-200 min-w-[200px] py-2 animate-in fade-in-0 zoom-in-95 duration-200"
              >
                <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-200">
                  Show/Hide Columns
                </div>
                {columns.map((column) => (
                  <label
                    key={String(column.key)}
                    className="flex items-center gap-3 px-3 py-2 hover:bg-gray-50 cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      checked={visibleColumns[String(column.key)]}
                      onChange={() => toggleColumnVisibility(String(column.key))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{column.label}</span>
                  </label>
                ))}
              </div>
            )}
          </div>

          {/* Download Selected Button */}
          <button
            onClick={handleDownloadSelected}
            disabled={selectedRows.size === 0}
            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2.5 rounded-md text-sm font-medium flex items-center gap-2"
            title={`Download selected rows (${selectedRows.size} selected)`}
          >
            <Download className="h-3.5 w-3.5" />
            Download ({selectedRows.size})
          </button>

          {/* Add New User Button */}
          <Button
            onClick={handleAddNewUser}
            className="flex items-center justify-center py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors text-sm font-medium"
          >
            <Plus className="h-4 w-4 mr-2" />
            <span>Add New User</span>
          </Button>

          {/* Add New User Dialog */}
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>New Account</DialogTitle>
                <DialogDescription>
                  Create a new user account. Fill in all fields to continue.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="username" className="text-right">
                    Username
                  </Label>
                  <Input
                    id="username"
                    name="username"
                    value={newUserData.username}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={newUserData.name}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="email" className="text-right">
                    Email
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={newUserData.email}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="userType" className="text-right">
                    User Type
                  </Label>
                  <Select
                    value={newUserData.userType}
                    onValueChange={handleUserTypeChange}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select user type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Manager">Manager</SelectItem>
                      <SelectItem value="Recruiter">Recruiter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="button" onClick={handleSubmitNewUser}>
                  Create Account
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        {/* </div> */}
      {/* // </div> */}

      {/* Table with fixed header and scrollable body */}
      <AnimatedTableWrapper
        isLoading={isLoading}
        className="border border-gray-200 rounded-md overflow-hidden flex-1"
      >
        <div className="overflow-auto h-[478px]" style={{ scrollbarGutter: 'stable' }}>
          <table className="w-full table-fixed divide-y divide-gray-200" style={{ minWidth: '1200px' }}>
            <thead className="bg-gray-50 sticky top-0 z-10">
              <tr>
                {/* Select All Checkbox */}
                <th className="sticky top-0 z-10 px-3 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-12">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                {filteredColumns.map((column) => (
                  <th
                    key={String(column.key)}
                    scope="col"
                    className={`sticky top-0 z-10 px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider cursor-pointer bg-gray-50 border-b border-gray-200 ${
                      column.key === "username" ? "w-32" :
                      column.key === "firstName" ? "w-40" :
                      column.key === "email" ? "w-48" :
                      column.key === "userType" ? "w-32" :
                      "w-24"
                    }`}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      <AnimatedSortIcon
                        direction={
                          sortConfig.key === column.key
                            ? sortConfig.direction === "ascending"
                              ? "ascending"
                              : "descending"
                            : null
                        }
                        active={sortConfig.key === column.key}
                        size={14}
                      />
                    </div>
                  </th>
                ))}
                {/* Action Column Headers */}
                <th className="sticky top-0 z-10 px-3 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-20">
                  Verify
                </th>
                <th className="sticky top-0 z-10 px-3 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-28">
                  Account Deactivate
                </th>
                <th className="sticky top-0 z-10 px-3 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-24">
                  Peer Reviewer
                </th>
                <th className="sticky top-0 z-10 px-3 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-20">
                  Delete
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentUsers.length > 0 ? (
                currentUsers.map((user, index) => (
                  <AnimatedTableRow key={user.id} index={index}>
                    {/* Row Selection Checkbox */}
                    <td className="px-3 py-3 whitespace-nowrap text-center">
                      <input
                        type="checkbox"
                        checked={selectedRows.has(user.id)}
                        onChange={(e) => handleSelectRow(user.id, e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    {filteredColumns.map((column) => (
                      <td
                        key={`${user.id}-${String(column.key)}`}
                        className="px-3 py-2 text-xs text-gray-800 font-medium whitespace-nowrap"
                      >
                        {column.key === "firstName" ? (
                          `${user.firstName} ${user.lastName}`
                        ) : column.key === "userType" ? (
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-[11px] font-bold ${
                              user.userType === "Manager"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-green-100 text-green-800"
                            }`}
                          >
                            {user.userType}
                          </span>
                        ) : (
                          String(user[column.key])
                        )}
                      </td>
                    ))}

                    {/* Action Switches */}
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-center">
                      <Switch
                        checked={user.isVerified}
                        onCheckedChange={(checked: boolean) => handleToggleVerification(user, checked)}
                        title={user.isVerified ? "Verified" : "Not Verified"}
                      />
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-center">
                      <Switch
                        checked={user.isActive}
                        onCheckedChange={(checked: boolean) => handleToggleActivation(user, checked)}
                        title={user.isActive ? "Active" : "Deactivated"}
                      />
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-center">
                      <Switch
                        checked={user.isPeerReviewer}
                        onCheckedChange={(checked: boolean) => handleTogglePeerReviewer(user, checked)}
                        title={user.isPeerReviewer ? "Peer Reviewer" : "Not Peer Reviewer"}
                      />
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-center">
                      <button
                        onClick={() => handleDeleteUser(user.id)}
                        className="text-gray-400 hover:text-red-600"
                        title="Delete User"
                      >
                        <Trash2 className="h-5 w-5 mx-auto" />
                      </button>
                    </td>
                  </AnimatedTableRow>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={
                      filteredColumns.length + 5
                    } /* Add 4 for the action columns (Verify, Deactivate, Peer Reviewer, Delete) and 1 for the checkbox column */
                    className="px-3 py-3 text-center text-sm text-gray-500"
                  >
                    {loading ? 'Loading users...' : error ? `Error: ${error}` : 'No users found'}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </AnimatedTableWrapper>

      {/* Pagination */}
      <div className="flex items-center justify-between mt-4 text-sm text-gray-700">
        <div>
          Showing {indexOfFirstItem + 1} to{" "}
          {Math.min(indexOfLastItem, filteredUsers.length)} of{" "}
          {filteredUsers.length} users
        </div>
        <AnimatedPagination
          currentPage={currentPage}
          totalPages={Math.ceil(filteredUsers.length / itemsPerPage)}
          onPageChange={paginate}
        />
      </div>
    </div>
  );
}
