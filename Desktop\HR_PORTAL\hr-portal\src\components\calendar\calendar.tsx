import React, { useState, useEffect, useMemo } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface Meeting {
  id?: string;
  date: string;
  startTime: string;
  endTime: string;
  title: string;
  attendees?: string[];
  attendeesOptional?: string[];
  description?: string;
  timezone?: string;
}

interface Timezone {
  value: string;
  label: string;
}

const TIME_SLOT_START_HOUR = 9;
const TIME_SLOT_END_HOUR = 17;
const TIME_SLOT_INTERVAL = 30;
const MONTH_NAMES = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const initialScheduledMeetings: Meeting[] = [
  {
    id: "m1",
    date: "2025-05-15",
    startTime: "10:00 AM",
    endTime: "10:30 AM",
    title: "Team Standup",
  },
  {
    id: "m2",
    date: "2025-05-15",
    startTime: "2:00 PM",
    endTime: "3:00 PM",
    title: "Project Alpha Review",
  },
  {
    id: "m3",
    date: "2025-05-20",
    startTime: "11:00 AM",
    endTime: "11:30 AM",
    title: "Client Call",
  },
  {
    id: "m4",
    date: "2025-05-05",
    startTime: "9:30 AM",
    endTime: "10:00 AM",
    title: "Morning Check-in",
  },
  {
    id: "m5",
    date: "2025-05-06",
    startTime: "3:30 PM",
    endTime: "4:00 PM",
    title: "Design Sync",
  },
];

const formatDateToYYYYMMDD = (date: Date): string => {
  return date.toLocaleDateString("en-CA");
};

const timeToMinutes = (timeStr?: string): number => {
  if (!timeStr) return 0;
  try {
    const [time, modifier] = timeStr.split(" ");
    let [hours, minutes] = time.split(":").map(Number);
    if (!modifier || isNaN(hours) || isNaN(minutes)) return 0;

    if (modifier.toUpperCase() === "PM" && hours < 12) hours += 12;
    if (modifier.toUpperCase() === "AM" && hours === 12) hours = 0;
    return hours * 60 + minutes;
  } catch (error) {
    console.error("Error parsing time string:", timeStr, error);
    return 0;
  }
};

const getTimezones = (): Timezone[] => {
  return [
    { value: "GMT-12:00", label: "(GMT-12:00) International Date Line West" },
    { value: "GMT-11:00", label: "(GMT-11:00) Midway Island, Samoa" },
    { value: "GMT-10:00", label: "(GMT-10:00) Hawaii" },
    { value: "GMT-09:00", label: "(GMT-09:00) Alaska" },
    { value: "GMT-08:00", label: "(GMT-08:00) Pacific Time (US & Canada)" },
    { value: "GMT-07:00", label: "(GMT-07:00) Mountain Time (US & Canada)" },
    { value: "GMT-06:00", label: "(GMT-06:00) Central Time (US & Canada)" },
    { value: "GMT-05:00", label: "(GMT-05:00) Eastern Time (US & Canada)" },
    { value: "GMT-04:00", label: "(GMT-04:00) Atlantic Time (Canada)" },
    { value: "GMT-03:30", label: "(GMT-03:30) Newfoundland" },
    { value: "GMT-03:00", label: "(GMT-03:00) Buenos Aires, Georgetown" },
    { value: "GMT-02:00", label: "(GMT-02:00) Mid-Atlantic" },
    { value: "GMT-01:00", label: "(GMT-01:00) Azores" },
    {
      value: "GMT+00:00",
      label: "(GMT+00:00) Greenwich Mean Time : Dublin, Lisbon, London",
    },
    { value: "GMT+01:00", label: "(GMT+01:00) Amsterdam, Berlin, Rome, Paris" },
    { value: "GMT+02:00", label: "(GMT+02:00) Cairo, Helsinki, Jerusalem" },
    {
      value: "GMT+03:00",
      label: "(GMT+03:00) Moscow, St. Petersburg, Volgograd",
    },
    { value: "GMT+03:30", label: "(GMT+03:30) Tehran" },
    { value: "GMT+04:00", label: "(GMT+04:00) Abu Dhabi, Muscat" },
    { value: "GMT+04:30", label: "(GMT+04:30) Kabul" },
    { value: "GMT+05:00", label: "(GMT+05:00) Islamabad, Karachi, Tashkent" },
    {
      value: "GMT+05:30",
      label: "(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi (IST)",
    },
    { value: "GMT+05:45", label: "(GMT+05:45) Kathmandu" },
    { value: "GMT+06:00", label: "(GMT+06:00) Almaty, Novosibirsk" },
    { value: "GMT+06:30", label: "(GMT+06:30) Yangon (Rangoon)" },
    { value: "GMT+07:00", label: "(GMT+07:00) Bangkok, Hanoi, Jakarta" },
    {
      value: "GMT+08:00",
      label: "(GMT+08:00) Beijing, Perth, Singapore, Hong Kong",
    },
    { value: "GMT+09:00", label: "(GMT+09:00) Osaka, Sapporo, Tokyo" },
    { value: "GMT+09:30", label: "(GMT+09:30) Adelaide, Darwin" },
    {
      value: "GMT+10:00",
      label: "(GMT+10:00) Brisbane, Canberra, Melbourne, Sydney",
    },
    {
      value: "GMT+11:00",
      label: "(GMT+11:00) Magadan, Solomon Is., New Caledonia",
    },
    { value: "GMT+12:00", label: "(GMT+12:00) Auckland, Wellington" },
    { value: "GMT+13:00", label: "(GMT+13:00) Nuku'alofa" },
  ];
};

const getLikelyUserTimezoneValue = (): string => {
  const userTimezoneOffset = -new Date().getTimezoneOffset() / 60;
  let likelyUserTimezone = `GMT${
    userTimezoneOffset >= 0 ? "+" : ""
  }${userTimezoneOffset}:00`;
  if (userTimezoneOffset === 5.5) likelyUserTimezone = "GMT+05:30";
  if (userTimezoneOffset === 3.5) likelyUserTimezone = "GMT+03:30";
  if (userTimezoneOffset === 9.5) likelyUserTimezone = "GMT+09:30";
  if (userTimezoneOffset === -3.5) likelyUserTimezone = "GMT-03:30";
  if (userTimezoneOffset === -9.5) likelyUserTimezone = "GMT-09:30";
  return likelyUserTimezone;
};

const CalendarView: React.FC = () => {
  const [currentMonthDate, setCurrentMonthDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedStartTime, setSelectedStartTime] = useState<string>("");
  const [selectedEndTime, setSelectedEndTime] = useState<string>("");
  const [scheduledMeetings, setScheduledMeetings] = useState<Meeting[]>(
    initialScheduledMeetings
  );
  const [title, setTitle] = useState("");
  const [attendees, setAttendees] = useState("");
  const [attendeesOptional, setAttendeesOptional] = useState("");
  const [description, setDescription] = useState("");
  const [timezone, setTimezone] = useState<string>("");

  const today = useMemo(() => {
    const now = new Date();
    now.setHours(0, 0, 0, 0);
    return now;
  }, []);

  const timezones = useMemo(() => getTimezones(), []);

  useEffect(() => {
    const defaultTzValue = getLikelyUserTimezoneValue();
    const tzExists = timezones.some((tz) => tz.value === defaultTzValue);
    setTimezone(tzExists ? defaultTzValue : "");
  }, [timezones]);

  const currentYear = currentMonthDate.getFullYear();
  const currentMonth = currentMonthDate.getMonth();

  const daysInMonth = useMemo(
    () => new Date(currentYear, currentMonth + 1, 0).getDate(),
    [currentYear, currentMonth]
  );
  const firstDayOfMonth = useMemo(
    () => new Date(currentYear, currentMonth, 1).getDay(),
    [currentYear, currentMonth]
  );

  const meetingsByDate = useMemo(() => {
    const map = new Map<string, Meeting[]>();
    scheduledMeetings.forEach((meeting) => {
      const list = map.get(meeting.date) || [];
      list.push(meeting);
      list.sort(
        (a, b) => timeToMinutes(a.startTime) - timeToMinutes(b.startTime)
      );
      map.set(meeting.date, list);
    });
    return map;
  }, [scheduledMeetings]);

  const handlePrevMonth = () => {
    setCurrentMonthDate(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentMonthDate(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
  };

  const handleDateClick = (day: number) => {
    const clickedDate = new Date(currentYear, currentMonth, day);
    clickedDate.setHours(0, 0, 0, 0);
    setSelectedDate(clickedDate);
    setSelectedStartTime("");
    setSelectedEndTime("");
  };

  const handleStartTimeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedStartTime(e.target.value);
    setSelectedEndTime("");
  };

  const handleEndTimeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedEndTime(e.target.value);
  };

  const handleSaveMeeting = () => {
    if (!selectedDate || !selectedStartTime || !selectedEndTime || !timezone) {
      alert(
        "Please ensure a date, start time, end time, and timezone are selected."
      );
      return;
    }

    const newMeeting: Meeting = {
      date: formatDateToYYYYMMDD(selectedDate),
      startTime: selectedStartTime,
      endTime: selectedEndTime,
      title: title || "Untitled Meeting",
      attendees: attendees
        .split(",")
        .map((e) => e.trim())
        .filter((e) => e),
      attendeesOptional: attendeesOptional
        .split(",")
        .map((e) => e.trim())
        .filter((e) => e),
      description: description,
      timezone: timezone,
      id: `m${Date.now()}`,
    };

    const newMeetingStartMinutes = timeToMinutes(newMeeting.startTime);
    const newMeetingEndMinutes = timeToMinutes(newMeeting.endTime);
    const meetingsOnDate = meetingsByDate.get(newMeeting.date) || [];

    const conflict = meetingsOnDate.find((existing) => {
      const existingStartMinutes = timeToMinutes(existing.startTime);
      const existingEndMinutes = timeToMinutes(existing.endTime);
      return (
        newMeetingStartMinutes < existingEndMinutes &&
        existingStartMinutes < newMeetingEndMinutes
      );
    });

    if (conflict) {
      alert(
        `Scheduling conflict detected with "${conflict.title}" (${conflict.startTime} - ${conflict.endTime}). Please choose a different time.`
      );
      return;
    }

    setScheduledMeetings((prev) => [...prev, newMeeting]);

    alert(
      `Meeting Scheduled!\n\nTitle: ${newMeeting.title}\nDate: ${
        newMeeting.date
      }\nTime: ${newMeeting.startTime} - ${newMeeting.endTime} (${
        newMeeting.timezone
      })\nAttendees: ${
        newMeeting.attendees?.join(", ") || "None"
      }\nDescription: ${newMeeting.description || "None"}`
    );

    setTitle("");
    setAttendees("");
    setAttendeesOptional("");
    setDescription("");
    setSelectedDate(null);
    setSelectedStartTime("");
    setSelectedEndTime("");
  };

  const handleClose = () => {
    setSelectedDate(null);
    setSelectedStartTime("");
    setSelectedEndTime("");
    setTitle("");
    setAttendees("");
    setAttendeesOptional("");
    setDescription("");
    setTimezone(getLikelyUserTimezoneValue());
    console.log("Close button clicked - Form reset");
  };

  const availableStartTimes = useMemo((): string[] => {
    if (!selectedDate) return [];

    const now = new Date();
    const selectedDateString = formatDateToYYYYMMDD(selectedDate);
    const meetingsOnSelectedDate = meetingsByDate.get(selectedDateString) || [];

    const blockedSlots = new Set<number>();
    meetingsOnSelectedDate.forEach((meeting) => {
      const startMinutes = timeToMinutes(meeting.startTime);
      const endMinutes = timeToMinutes(meeting.endTime);
      for (let m = startMinutes; m < endMinutes; m += TIME_SLOT_INTERVAL) {
        blockedSlots.add(m);
      }
    });

    const options: string[] = [];
    const tempDate = new Date(selectedDate);
    tempDate.setSeconds(0, 0);

    for (let hour = TIME_SLOT_START_HOUR; hour < TIME_SLOT_END_HOUR; hour++) {
      for (let minute = 0; minute < 60; minute += TIME_SLOT_INTERVAL) {
        tempDate.setHours(hour, minute);
        const currentSlotMinutes = hour * 60 + minute;

        if (selectedDate.getTime() === today.getTime() && tempDate < now) {
          continue;
        }

        if (blockedSlots.has(currentSlotMinutes)) {
          continue;
        }

        options.push(
          tempDate.toLocaleTimeString("en-US", {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          })
        );
      }
    }
    return options;
  }, [selectedDate, today, meetingsByDate]);

  const availableEndTimes = useMemo((): string[] => {
    if (!selectedDate || !selectedStartTime) return [];

    const startMinutes = timeToMinutes(selectedStartTime);
    const selectedDateString = formatDateToYYYYMMDD(selectedDate);
    const meetingsOnSelectedDate = meetingsByDate.get(selectedDateString) || [];

    let nextMeetingStartMinutes = Infinity;
    meetingsOnSelectedDate.forEach((meeting) => {
      const meetingStart = timeToMinutes(meeting.startTime);
      if (
        meetingStart > startMinutes &&
        meetingStart < nextMeetingStartMinutes
      ) {
        nextMeetingStartMinutes = meetingStart;
      }
    });

    const options: string[] = [];
    const tempDate = new Date(selectedDate);
    tempDate.setSeconds(0, 0);
    const endHourLimit = TIME_SLOT_END_HOUR;

    for (let hour = TIME_SLOT_START_HOUR; hour <= endHourLimit; hour++) {
      for (let minute = 0; minute < 60; minute += TIME_SLOT_INTERVAL) {
        tempDate.setHours(hour, minute);
        const currentSlotMinutes = hour * 60 + minute;

        if (currentSlotMinutes <= startMinutes) {
          continue;
        }

        if (currentSlotMinutes >= nextMeetingStartMinutes) {
          continue;
        }

        if (
          hour >= endHourLimit &&
          minute > 0 &&
          currentSlotMinutes > startMinutes
        )
          break;

        options.push(
          tempDate.toLocaleTimeString("en-US", {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          })
        );
      }
    }

    const absoluteEndTime = new Date(selectedDate);
    absoluteEndTime.setHours(TIME_SLOT_END_HOUR, 0, 0, 0);
    const absoluteEndTimeMinutes = TIME_SLOT_END_HOUR * 60;
    const absoluteEndTimeString = absoluteEndTime.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });

    if (
      absoluteEndTimeMinutes > startMinutes &&
      absoluteEndTimeMinutes < nextMeetingStartMinutes &&
      !options.includes(absoluteEndTimeString)
    ) {
      options.push(absoluteEndTimeString);
      options.sort((a, b) => timeToMinutes(a) - timeToMinutes(b));
    }

    return options;
  }, [selectedDate, selectedStartTime, meetingsByDate]);

  const renderCalendarDays = () => {
    const days = [];
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(<div key={`empty-${i}`} className="aspect-square"></div>);
    }

    for (let day = 1; day <= daysInMonth; day++) {
      const cellDate = new Date(currentYear, currentMonth, day);
      cellDate.setHours(0, 0, 0, 0);
      const cellDateString = formatDateToYYYYMMDD(cellDate);

      const isToday = cellDate.getTime() === today.getTime();
      const isSelected = selectedDate?.getTime() === cellDate.getTime();
      const isPast = cellDate < today;
      const hasMeeting = meetingsByDate.has(cellDateString);

      // Reduced padding from p-2 to p-1
      let buttonClasses =
        "p-1 rounded-full text-sm aspect-square flex items-center justify-center relative focus:outline-none focus:ring-1 focus:ring-blue-400";
      if (isPast) {
        buttonClasses += " text-gray-300 cursor-not-allowed";
      } else {
        buttonClasses += " hover:bg-blue-100";
      }
      if (isSelected) {
        buttonClasses += " bg-blue-500 text-white font-bold";
      }
      if (isToday) {
        buttonClasses += " border-2 border-blue-400 font-semibold";
        if (isSelected) buttonClasses += " border-blue-600";
      }
      if (hasMeeting) {
        buttonClasses += " has-meeting-indicator";
      }

      days.push(
        <button
          key={day}
          onClick={() => !isPast && handleDateClick(day)}
          disabled={isPast}
          className={buttonClasses}
        >
          {day}
          {hasMeeting && !isPast && (
            <span
              className={`absolute bottom-0.5 left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full ${
                isSelected ? "bg-white" : "bg-amber-500"
              }`}
            ></span> // Adjusted dot size/position slightly
          )}
        </button>
      );
    }
    return days;
  };

  const canSave =
    selectedDate && selectedStartTime && selectedEndTime && timezone;
  const meetingsForSelectedDate = selectedDate
    ? meetingsByDate.get(formatDateToYYYYMMDD(selectedDate)) || []
    : [];

  return (
    <div className="w-full h-full flex flex-col font-inter">
      <div className="bg-white p-6 w-full flex-1">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
          {/* Column 1: Calendar, Scheduled Meetings, Time Selection */}
          <div className="flex flex-col space-y-4">
            {/* Calendar Section */}
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <button
                  onClick={handlePrevMonth}
                  className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-400"
                >
                  <ChevronLeft className="w-5 h-5 text-gray-600" />
                </button>
                <h2 className="text-lg font-semibold text-gray-700">
                  {MONTH_NAMES[currentMonth]} {currentYear}
                </h2>
                <button
                  onClick={handleNextMonth}
                  className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-400"
                >
                  <ChevronRight className="w-5 h-5 text-gray-600" />
                </button>
              </div>
              <div className="grid grid-cols-7 gap-1 text-center text-xs text-gray-500 mb-2">
                <div>Sun</div>
                <div>Mon</div>
                <div>Tue</div>
                <div>Wed</div>
                <div>Thu</div>
                <div>Fri</div>
                <div>Sat</div>
              </div>
              <div className="grid grid-cols-7 gap-1">
                {" "}
                {/* Kept gap-1 between buttons */}
                {renderCalendarDays()}
              </div>
            </div>

            {/* Scheduled Meetings Display */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">
                Scheduled Meetings for Selected Date
              </h3>
              <div className="border border-gray-200 rounded-md p-2 min-h-[60px] max-h-24 overflow-y-auto bg-gray-50 text-sm scheduled-meetings-container">
                {!selectedDate ? (
                  <p className="text-gray-500 italic text-center">
                    Select a date to see scheduled meetings.
                  </p>
                ) : meetingsForSelectedDate.length > 0 ? (
                  meetingsForSelectedDate.map((meeting, index) => (
                    <div key={meeting.id || index} className="py-1">
                      {meeting.startTime} - {meeting.endTime}: {meeting.title}
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 italic text-center">
                    No meetings scheduled for this date.
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Column 2: Form Fields */}
          <div className="flex flex-col space-y-4">
            <div>
              <label
                htmlFor="title"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Title
              </label>
              <input
                type="text"
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Add title"
                className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label
                htmlFor="attendees"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Attendees
              </label>
              <input
                type="text"
                id="attendees"
                value={attendees}
                onChange={(e) => setAttendees(e.target.value)}
                placeholder="Search or add email (comma-separated)"
                className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label
                htmlFor="attendees-optional"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Attendees (optional)
              </label>
              <input
                type="text"
                id="attendees-optional"
                value={attendeesOptional}
                onChange={(e) => setAttendeesOptional(e.target.value)}
                placeholder="Search or add email (comma-separated)"
                className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label
                htmlFor="description"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Description
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                placeholder="Enter your text here"
                className="w-full p-2 border border-gray-300 rounded-md text-sm resize-none focus:ring-blue-500 focus:border-blue-500"
              ></textarea>
            </div>

            <div>
              <label
                htmlFor="timezone"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Time Zone
              </label>
              <select
                id="timezone"
                value={timezone}
                onChange={(e) => setTimezone(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md text-sm bg-white focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="" disabled>
                  Select a time zone
                </option>
                {timezones.map((tz) => (
                  <option key={tz.value} value={tz.value}>
                    {tz.label}
                  </option>
                ))}
              </select>
            </div>
            {/* Time Selection */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="start-time"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Start Time *
                </label>
                <select
                  id="start-time"
                  value={selectedStartTime}
                  onChange={handleStartTimeChange}
                  disabled={!selectedDate || availableStartTimes.length === 0}
                  className="w-full p-2 border border-gray-300 rounded-md text-sm bg-white focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
                >
                  <option value="" disabled>
                    {!selectedDate
                      ? "Select date first"
                      : availableStartTimes.length === 0
                      ? "No available times"
                      : "Select time"}
                  </option>
                  {availableStartTimes.map((time) => (
                    <option key={time} value={time}>
                      {time}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label
                  htmlFor="end-time"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  End Time *
                </label>
                <select
                  id="end-time"
                  value={selectedEndTime}
                  onChange={handleEndTimeChange}
                  disabled={
                    !selectedStartTime || availableEndTimes.length === 0
                  }
                  className="w-full p-2 border border-gray-300 rounded-md text-sm bg-white focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
                >
                  <option value="" disabled>
                    {!selectedStartTime
                      ? "Select start time"
                      : availableEndTimes.length === 0
                      ? "No available times"
                      : "Select time"}
                  </option>
                  {availableEndTimes.map((time) => (
                    <option key={time} value={time}>
                      {time}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            {/* Spacer to push buttons down */}
            <div className="flex-grow"></div>

            {/* Selected Info Display */}
            {selectedDate && selectedStartTime && selectedEndTime && (
              <div className="p-3 bg-blue-50 rounded-md border border-blue-200 text-center text-sm text-blue-800">
                Selected for new meeting:{" "}
                <span className="font-semibold">
                  {selectedDate.toLocaleDateString("en-US", {
                    weekday: "short",
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                  })}
                  , {selectedStartTime} - {selectedEndTime}
                </span>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end gap-3 pt-2">
              <button
                onClick={handleClose}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-5 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-75"
              >
                Close
              </button>
              <button
                onClick={handleSaveMeeting}
                disabled={!canSave}
                className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-5 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-75 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Save New Meeting
              </button>
            </div>
          </div>
        </div>
      </div>
      <style>{`
                .has-meeting-indicator { position: relative; }
                .scheduled-meetings-container::-webkit-scrollbar { display: none; }
                .scheduled-meetings-container { -ms-overflow-style: none; scrollbar-width: none; }
                body { font-family: 'Inter', sans-serif; }
            `}</style>
    </div>
  );
};

export default CalendarView;
