import { useState, useEffect, useMemo, useCallback } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select } from "@/components/ui/select";
import { useUser } from "@/contexts/user-context";

// Define types
interface JobRequirement {
  id: string;
  title: string;
  department: string;
  skills: string[];
  experience: string;
  education: string;
  status: "Active" | "Filled" | "On Hold";
}

interface CandidateProfile {
  id: string;
  name: string;
  email: string;
  skills: string[];
  experience: number;
  education: string;
  resumeUrl: string;
  matchScore?: number;
  matchDetails?: {
    skill: string;
    matched: boolean;
  }[];
}

export function ProfileAnalysis() {
  // Using useUser hook for future functionality
  useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [requirements, setRequirements] = useState<JobRequirement[]>([]);
  const [selectedRequirement, setSelectedRequirement] = useState<string>("");
  const [candidates, setCandidates] = useState<CandidateProfile[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCandidate, setSelectedCandidate] = useState<CandidateProfile | null>(null);

  // Simulate loading data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1200));
      
      // Mock job requirements data
      const mockRequirements: JobRequirement[] = [
        {
          id: "req1",
          title: "Senior Frontend Developer",
          department: "Engineering",
          skills: ["React", "TypeScript", "CSS", "HTML", "Redux", "Jest", "Responsive Design"],
          experience: "5+ years",
          education: "Bachelor's in Computer Science or related field",
          status: "Active"
        },
        {
          id: "req2",
          title: "UX Designer",
          department: "Design",
          skills: ["Figma", "User Research", "Prototyping", "Design Systems", "UI Design", "Accessibility"],
          experience: "3+ years",
          education: "Bachelor's in Design or related field",
          status: "Active"
        },
        {
          id: "req3",
          title: "Full Stack Developer",
          department: "Engineering",
          skills: ["JavaScript", "React", "Node.js", "MongoDB", "Express", "RESTful APIs", "Git"],
          experience: "4+ years",
          education: "Bachelor's in Computer Science or related field",
          status: "Active"
        },
        {
          id: "req4",
          title: "Product Manager",
          department: "Product",
          skills: ["Roadmapping", "User Research", "Agile", "Analytics", "Product Strategy", "Stakeholder Management"],
          experience: "5+ years",
          education: "Bachelor's in Business, Computer Science, or related field",
          status: "Active"
        },
        {
          id: "req5",
          title: "DevOps Engineer",
          department: "Engineering",
          skills: ["AWS", "Docker", "Kubernetes", "CI/CD", "Linux", "Terraform", "Monitoring"],
          experience: "3+ years",
          education: "Bachelor's in Computer Science or related field",
          status: "Active"
        }
      ];
      
      setRequirements(mockRequirements);
      setIsLoading(false);
    };
    
    loadData();
  }, []);

  // Load and analyze candidate profiles when a requirement is selected
  useEffect(() => {
    if (selectedRequirement) {
      setIsLoading(true);
      
      const analyzeProfiles = async () => {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Find the selected requirement
        const requirement = requirements.find(r => r.id === selectedRequirement);
        if (!requirement) {
          setIsLoading(false);
          return;
        }
        
        // Mock candidate profiles with match scoring
        const mockCandidates: CandidateProfile[] = [
          {
            id: "cand1",
            name: "Alex Johnson",
            email: "<EMAIL>",
            skills: ["React", "TypeScript", "Redux", "HTML", "CSS", "JavaScript", "Node.js"],
            experience: 6,
            education: "M.S. in Computer Science",
            resumeUrl: "/resumes/alex-johnson.pdf"
          },
          {
            id: "cand2",
            name: "Priya Sharma",
            email: "<EMAIL>",
            skills: ["Figma", "User Research", "Prototyping", "UI Design", "User Testing", "Sketch"],
            experience: 4,
            education: "B.A. in Graphic Design",
            resumeUrl: "/resumes/priya-sharma.pdf"
          },
          {
            id: "cand3",
            name: "Thomas Lee",
            email: "<EMAIL>",
            skills: ["Product Strategy", "Agile", "User Research", "Analytics", "Roadmapping", "Stakeholder Management"],
            experience: 7,
            education: "MBA, Product Management",
            resumeUrl: "/resumes/thomas-lee.pdf"
          },
          {
            id: "cand4",
            name: "Elena Rodriguez",
            email: "<EMAIL>",
            skills: ["Python", "Data Science", "Machine Learning", "SQL", "Tableau", "R", "Statistics"],
            experience: 5,
            education: "Ph.D. in Computer Science",
            resumeUrl: "/resumes/elena-rodriguez.pdf"
          },
          {
            id: "cand5",
            name: "David Kim",
            email: "<EMAIL>",
            skills: ["AWS", "Docker", "Kubernetes", "CI/CD", "Terraform", "Linux", "Ansible"],
            experience: 4,
            education: "B.S. in Computer Engineering",
            resumeUrl: "/resumes/david-kim.pdf"
          },
          {
            id: "cand6",
            name: "Sarah Wilson",
            email: "<EMAIL>",
            skills: ["JavaScript", "React", "Node.js", "MongoDB", "Express", "GraphQL", "TypeScript"],
            experience: 3,
            education: "B.S. in Information Technology",
            resumeUrl: "/resumes/sarah-wilson.pdf"
          }
        ];
        
        // Calculate match scores and add match details
        const analyzedCandidates = mockCandidates.map(candidate => {
          // Calculate skill match percentage
          const matchDetails = requirement.skills.map(skill => ({
            skill,
            matched: candidate.skills.includes(skill)
          }));
          
          const matchedSkills = matchDetails.filter(detail => detail.matched).length;
          const matchScore = (matchedSkills / requirement.skills.length) * 100;
          
          return {
            ...candidate,
            matchScore: Math.round(matchScore),
            matchDetails
          };
        });
        
        // Sort by match score (highest first)
        const sortedCandidates = analyzedCandidates.sort((a, b) => 
          (b.matchScore || 0) - (a.matchScore || 0)
        );
        
        setCandidates(sortedCandidates);
        setIsLoading(false);
      };
      
      analyzeProfiles();
    } else {
      setCandidates([]);
    }
  }, [selectedRequirement, requirements]);

  // Filter candidates based on search query - using useMemo to prevent unnecessary re-filtering
  const filteredCandidates = useMemo(() => {
    return candidates.filter(candidate => 
      candidate.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      candidate.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      candidate.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  }, [candidates, searchQuery]);

  // Memoize candidate selection handler to prevent unnecessary re-renders
  const handleCandidateSelect = useCallback((candidate: CandidateProfile) => {
    setSelectedCandidate(candidate);
  }, []);

  if (isLoading && !requirements.length) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <svg
            className="animate-spin h-10 w-10 text-blue-600 mx-auto mb-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <p className="text-gray-600">Loading requirements...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col space-y-6 p-1 overflow-auto">
      <div className="bg-white rounded-lg shadow border border-gray-200 p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Profile Analysis</h1>
            <p className="text-gray-600">Analyze candidate profiles against job requirements</p>
          </div>
        </div>
        
        {/* Requirement selector */}
        <div className="mb-6">
          <Label htmlFor="requirement" className="text-base font-medium text-gray-800 mb-2 block">
            Select a Job Requirement
          </Label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Select

              value={selectedRequirement}
              onValueChange={setSelectedRequirement}

            >
              <option value="">Select a requirement</option>
              {requirements.map(req => (
                <option key={req.id} value={req.id}>
                  {req.title} ({req.department})
                </option>
              ))}
            </Select>
            
            <Input
              placeholder="Search candidates..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              disabled={!selectedRequirement}
            />
          </div>
        </div>
        
        {selectedRequirement && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Requirement details */}
            <div className="lg:col-span-1">
              <div className="bg-gray-50 rounded-lg border border-gray-200 p-4 h-full">
                <h2 className="text-lg font-semibold text-gray-800 mb-4">Requirement Details</h2>
                
                {isLoading ? (
                  <div className="flex items-center justify-center h-40">
                    <svg
                      className="animate-spin h-8 w-8 text-blue-600"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  </div>
                ) : (
                  <div>
                    {requirements.find(r => r.id === selectedRequirement) && (
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-700">Title</h3>
                          <p className="text-gray-900">{requirements.find(r => r.id === selectedRequirement)?.title}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-700">Department</h3>
                          <p className="text-gray-900">{requirements.find(r => r.id === selectedRequirement)?.department}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-700">Required Skills</h3>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {useMemo(() => 
                              requirements.find(r => r.id === selectedRequirement)?.skills.map(skill => (
                                <span key={skill} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                  {skill}
                                </span>
                              )), 
                              [requirements, selectedRequirement]
                            )}
                          </div>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-700">Experience</h3>
                          <p className="text-gray-900">{requirements.find(r => r.id === selectedRequirement)?.experience}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-700">Education</h3>
                          <p className="text-gray-900">{requirements.find(r => r.id === selectedRequirement)?.education}</p>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
            
            {/* Candidates list */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg border border-gray-200 h-full">
                <h2 className="text-lg font-semibold text-gray-800 p-4 border-b">
                  Matching Candidates
                  {!isLoading && (
                    <span className="text-sm font-normal text-gray-500 ml-2">
                      ({filteredCandidates.length} matches)
                    </span>
                  )}
                </h2>
                
                {isLoading ? (
                  <div className="flex items-center justify-center h-64">
                    <svg
                      className="animate-spin h-8 w-8 text-blue-600"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  </div>
                ) : filteredCandidates.length === 0 ? (
                  <div className="text-center py-12">
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      ></path>
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No matches found</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Try adjusting your search or select a different requirement.
                    </p>
                  </div>
                ) : (
                  <div className="divide-y divide-gray-200 max-h-[500px] overflow-y-auto">
                    {filteredCandidates.map((candidate, index) => (
                      <motion.div
                        key={candidate.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.05 }}
                        className={`p-4 cursor-pointer hover:bg-gray-50 ${
                          selectedCandidate?.id === candidate.id ? 'bg-blue-50' : ''
                        }`}
                        onClick={() => handleCandidateSelect(candidate)}
                      >
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-medium text-gray-900">{candidate.name}</h3>
                            <p className="text-sm text-gray-500">{candidate.email}</p>
                            <p className="text-sm text-gray-700 mt-1">
                              {candidate.experience} years • {candidate.education}
                            </p>
                            <div className="flex flex-wrap gap-1 mt-2">
                              {candidate.skills.slice(0, 5).map((skill, idx) => (
                                <span 
                                  key={idx} 
                                  className={`text-xs px-2 py-0.5 rounded-full ${
                                    useMemo(() => requirements.find(r => r.id === selectedRequirement)?.skills.includes(skill), [requirements, selectedRequirement, skill])
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-gray-100 text-gray-800'
                                  }`}
                                >
                                  {skill}
                                </span>
                              ))}
                              {candidate.skills.length > 5 && (
                                <span className="text-xs px-2 py-0.5 rounded-full bg-gray-100 text-gray-800">
                                  +{candidate.skills.length - 5} more
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex flex-col items-end">
                            <div className={`font-semibold text-lg ${
                              (candidate.matchScore || 0) >= 80 
                                ? 'text-green-600' 
                                : (candidate.matchScore || 0) >= 60 
                                ? 'text-amber-600' 
                                : 'text-red-600'
                            }`}>
                              {candidate.matchScore}%
                            </div>
                            <div className="text-xs text-gray-500">match</div>
                          </div>
                        </div>
                        
                        {selectedCandidate?.id === candidate.id && candidate.matchDetails && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="mt-4 border-t pt-3"
                          >
                            <h4 className="text-sm font-medium text-gray-700 mb-2">Skill Match Analysis</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {candidate.matchDetails.map((detail, idx) => (
                                <div 
                                  key={idx} 
                                  className={`text-sm py-1 px-2 rounded flex items-center ${
                                    detail.matched ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
                                  }`}
                                >
                                  <span className={`mr-1 ${detail.matched ? 'text-green-500' : 'text-red-500'}`}>
                                    {detail.matched ? '✓' : '✗'}
                                  </span>
                                  {detail.skill}
                                </div>
                              ))}
                            </div>
                            <div className="mt-3 flex justify-end">
                              <Button className="bg-blue-600 hover:bg-blue-700 mr-2">
                                View Resume
                              </Button>
                              <Button variant="outline">
                                Contact Candidate
                              </Button>
                            </div>
                          </motion.div>
                        )}
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
        
        {!selectedRequirement && !isLoading && (
          <div className="bg-gray-50 rounded-lg p-8 text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No requirement selected</h3>
            <p className="mt-1 text-sm text-gray-500">
              Select a job requirement to see matching candidates.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
