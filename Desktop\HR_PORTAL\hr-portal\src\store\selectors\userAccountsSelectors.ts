import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../index';
import { User, FilterTag } from '../slices/userAccountsSlice';

// Base selectors
export const selectUsers = (state: RootState) => state.userAccounts.users;
export const selectUsersLoading = (state: RootState) => state.userAccounts.loading;
export const selectUsersError = (state: RootState) => state.userAccounts.error;
export const selectSearchTags = (state: RootState) => state.userAccounts.searchTags;
export const selectAppliedTags = (state: RootState) => state.userAccounts.appliedTags;
export const selectCurrentPage = (state: RootState) => state.userAccounts.currentPage;
export const selectItemsPerPage = (state: RootState) => state.userAccounts.itemsPerPage;
export const selectSortConfig = (state: RootState) => state.userAccounts.sortConfig;
export const selectSelectedRows = (state: RootState) => state.userAccounts.selectedRows;
export const selectSelectAll = (state: RootState) => state.userAccounts.selectAll;
export const selectVisibleColumns = (state: RootState) => state.userAccounts.visibleColumns;

// Column definitions
export const userColumns = [
  { key: "username", label: "Username", sortable: true },
  { key: "firstName", label: "Name", sortable: true },
  { key: "email", label: "Email", sortable: true },
  { key: "userType", label: "User Type", sortable: true },
] as const;

// All possible columns for visibility toggle
export const allUserColumns = [
  { key: "username", label: "Username", sortable: true },
  { key: "firstName", label: "First Name", sortable: true },
  { key: "lastName", label: "Last Name", sortable: true },
  { key: "email", label: "Email", sortable: true },
  { key: "userType", label: "User Type", sortable: true },
  { key: "isVerified", label: "Verified", sortable: true },
  { key: "isActive", label: "Active", sortable: true },
  { key: "isPeerReviewer", label: "Peer Reviewer", sortable: true },
] as const;

// Suggestion priority for search
export const suggestionPriority: (keyof User)[] = [
  'username',
  'firstName',
  'lastName',
  'email',
  'userType',
];

// Memoized selector for filtered users
export const selectFilteredUsers = createSelector(
  [selectUsers, selectAppliedTags],
  (users, appliedTags) => {
    const groupedTags = appliedTags.reduce((acc, tag) => {
      const key = tag.column;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(tag);
      return acc;
    }, {} as Record<string, FilterTag[]>);

    return users.filter((user) => {
      // Tag-based Search Logic
      if (appliedTags.length > 0) {
        const matchesAllGroups = Object.values(groupedTags).every(tagGroup => {
          return tagGroup.some(tag => {
            const tagValue = tag.value.toLowerCase();
            const columnInfo = allUserColumns.find(c => c.label === tag.column);

            if (tag.column === 'Any') {
              return Object.values(user).some(val => String(val).toLowerCase().includes(tagValue));
            }
            if (!columnInfo) return false;

            const userValue = user[columnInfo.key as keyof User];
            return String(userValue).toLowerCase().includes(tagValue);
          });
        });

        if (!matchesAllGroups) {
          return false;
        }
      }

      return true;
    });
  }
);

// Memoized selector for sorted users
export const selectSortedUsers = createSelector(
  [selectFilteredUsers, selectSortConfig],
  (filteredUsers, sortConfig) => {
    return [...filteredUsers].sort((a, b) => {
      if (!sortConfig.key) return 0;
      
      let aValue: any = a[sortConfig.key];
      let bValue: any = b[sortConfig.key];
      
      if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1;
      return 0;
    });
  }
);

// Memoized selector for paginated users
export const selectPaginatedUsers = createSelector(
  [selectSortedUsers, selectCurrentPage, selectItemsPerPage],
  (sortedUsers, currentPage, itemsPerPage) => {
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return sortedUsers.slice(indexOfFirstItem, indexOfLastItem);
  }
);

// Selector for visible columns
export const selectFilteredColumns = createSelector(
  [selectVisibleColumns],
  (visibleColumns) => {
    return allUserColumns.filter(column => visibleColumns.includes(column.key as keyof User));
  }
);

// Selector for search suggestions
export const selectSearchSuggestions = createSelector(
  [selectFilteredUsers, (state: RootState, inputValue: string) => inputValue],
  (filteredUsers, inputValue) => {
    if (!inputValue) return [];
    
    const suggestions: { value: string; column: string }[] = [];
    
    // Use suggestion priority to order results
    suggestionPriority.forEach(key => {
      const column = allUserColumns.find(c => c.key === key);
      if (!column) return;
      
      filteredUsers.forEach(user => {
        const value = user[key];
        if (typeof value === 'string' && value.toLowerCase().includes(inputValue.toLowerCase())) {
          if (!suggestions.some(s => s.value === value && s.column === column.label)) {
            suggestions.push({ value, column: column.label });
          }
        }
      });
    });
    
    return suggestions.slice(0, 7);
  }
);

// Selector for selected users data
export const selectSelectedUsersData = createSelector(
  [selectUsers, selectSelectedRows],
  (users, selectedRows) => {
    return users.filter(user => selectedRows.includes(user.id));
  }
);

// Export columns for use in components
// export { userColumns, allUserColumns };
