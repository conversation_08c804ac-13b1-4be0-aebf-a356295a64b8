import { useState, useEffect, useMemo } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Select } from "@/components/ui/select";
import { useUser } from "@/contexts/user-context";

// Define data types
interface RecruiterAnalyticsData {
  summary: {
    activeRequirements: number;
    profilesReviewed: number;
    interviewsScheduled: number;
    offersExtended: number;
    hires: number;
    conversionRate: number;
  };
  requirementsBreakdown: {
    id: string;
    title: string;
    department: string;
    profilesReviewed: number;
    interviewsScheduled: number;
    offersExtended: number;
    hires: number;
    status: "Active" | "On Hold" | "Filled" | "Closed";
  }[];
  weeklyActivity: {
    week: string;
    profilesReviewed: number;
    interviewsScheduled: number;
    offersExtended: number;
    hires: number;
  }[];
  candidateStages: {
    stage: string;
    count: number;
    percentage: number;
  }[];
}

export function RecruiterAnalytics() {
  // Using useUser hook for future functionality
  useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState("last30days");
  const [analyticsData, setAnalyticsData] = useState<RecruiterAnalyticsData | null>(null);

  // Simulate API call to get analytics data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock analytics data for the logged-in recruiter
      setAnalyticsData({
        summary: {
          activeRequirements: 8,
          profilesReviewed: 64,
          interviewsScheduled: 38,
          offersExtended: 12,
          hires: 7,
          conversionRate: 0.11
        },
        requirementsBreakdown: [
          {
            id: "req1",
            title: "Senior Frontend Developer",
            department: "Engineering",
            profilesReviewed: 14,
            interviewsScheduled: 8,
            offersExtended: 3,
            hires: 2,
            status: "Active"
          },
          {
            id: "req2",
            title: "Product Designer",
            department: "Design",
            profilesReviewed: 12,
            interviewsScheduled: 7,
            offersExtended: 2,
            hires: 1,
            status: "Active"
          },
          {
            id: "req3",
            title: "Full Stack Developer",
            department: "Engineering",
            profilesReviewed: 18,
            interviewsScheduled: 10,
            offersExtended: 3,
            hires: 2,
            status: "Active"
          },
          {
            id: "req4",
            title: "Product Manager",
            department: "Product",
            profilesReviewed: 10,
            interviewsScheduled: 6,
            offersExtended: 2,
            hires: 1,
            status: "Active"
          },
          {
            id: "req5",
            title: "QA Engineer",
            department: "Engineering",
            profilesReviewed: 10,
            interviewsScheduled: 7,
            offersExtended: 2,
            hires: 1,
            status: "Active"
          }
        ],
        weeklyActivity: [
          { week: "Apr 1-7", profilesReviewed: 12, interviewsScheduled: 7, offersExtended: 2, hires: 1 },
          { week: "Apr 8-14", profilesReviewed: 15, interviewsScheduled: 9, offersExtended: 3, hires: 2 },
          { week: "Apr 15-21", profilesReviewed: 18, interviewsScheduled: 10, offersExtended: 4, hires: 2 },
          { week: "Apr 22-28", profilesReviewed: 19, interviewsScheduled: 12, offersExtended: 3, hires: 2 }
        ],
        candidateStages: [
          { stage: "Initial Screening", count: 28, percentage: 43.75 },
          { stage: "Technical Interview", count: 15, percentage: 23.44 },
          { stage: "Hiring Manager", count: 9, percentage: 14.06 },
          { stage: "Offer Extended", count: 5, percentage: 7.81 },
          { stage: "Hired", count: 3, percentage: 4.69 },
          { stage: "Rejected", count: 4, percentage: 6.25 }
        ]
      });
      
      setIsLoading(false);
    };
    
    fetchData();
  }, [timeRange]);

  const renderActivityChart = (data: any[]) => {
    const maxValue = Math.max(...data.flatMap(d => [d.profilesReviewed, d.interviewsScheduled, d.offersExtended, d.hires]));
    const normalize = (value: number) => (value / maxValue) * 100;
    
    return (
      <div className="flex flex-col">
        <div className="flex items-end h-56 space-x-6 mt-4">
          {data.map((week, index) => (
            <div key={week.week} className="flex-1 flex flex-col items-center">
              <div className="w-full flex justify-around space-x-1 h-44">
                <motion.div 
                  className="bg-blue-400 rounded-t w-3"
                  initial={{ height: 0 }}
                  animate={{ height: `${normalize(week.profilesReviewed)}%` }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                />
                <motion.div 
                  className="bg-purple-400 rounded-t w-3"
                  initial={{ height: 0 }}
                  animate={{ height: `${normalize(week.interviewsScheduled)}%` }}
                  transition={{ duration: 0.5, delay: index * 0.1 + 0.1 }}
                />
                <motion.div 
                  className="bg-amber-400 rounded-t w-3"
                  initial={{ height: 0 }}
                  animate={{ height: `${normalize(week.offersExtended)}%` }}
                  transition={{ duration: 0.5, delay: index * 0.1 + 0.2 }}
                />
                <motion.div 
                  className="bg-green-400 rounded-t w-3"
                  initial={{ height: 0 }}
                  animate={{ height: `${normalize(week.hires)}%` }}
                  transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
                />
              </div>
              <div className="text-xs font-medium text-gray-600 mt-2">{week.week}</div>
            </div>
          ))}
        </div>
        <div className="flex justify-center mt-4 space-x-6">
          <div className="flex items-center">
            <div className="h-3 w-3 bg-blue-400 rounded mr-1"></div>
            <span className="text-xs text-gray-600">Profiles</span>
          </div>
          <div className="flex items-center">
            <div className="h-3 w-3 bg-purple-400 rounded mr-1"></div>
            <span className="text-xs text-gray-600">Interviews</span>
          </div>
          <div className="flex items-center">
            <div className="h-3 w-3 bg-amber-400 rounded mr-1"></div>
            <span className="text-xs text-gray-600">Offers</span>
          </div>
          <div className="flex items-center">
            <div className="h-3 w-3 bg-green-400 rounded mr-1"></div>
            <span className="text-xs text-gray-600">Hires</span>
          </div>
        </div>
      </div>
    );
  };

  const renderPipeline = (data: any[]) => {
    const getColor = (index: number) => {
      const colors = [
        "bg-blue-500",
        "bg-indigo-500",
        "bg-violet-500",
        "bg-purple-500",
        "bg-green-500",
        "bg-red-500"
      ];
      return colors[index % colors.length];
    };
    
    return (
      <div className="space-y-4 mt-4">
        <div className="w-full h-10 flex rounded-full overflow-hidden">
          {data.map((stage, index) => (
            <motion.div
              key={stage.stage}
              className={`${getColor(index)} h-full`}
              style={{ width: `${stage.percentage}%` }}
              initial={{ width: 0 }}
              animate={{ width: `${stage.percentage}%` }}
              transition={{ duration: 0.8, delay: 0.2 }}
            />
          ))}
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 mt-2">
          {data.map((stage, index) => (
            <div key={stage.stage} className="flex flex-col items-center">
              <div className={`w-4 h-4 ${getColor(index)} rounded-full mb-1`}></div>
              <div className="text-xs font-medium text-center">{stage.stage}</div>
              <div className="text-xs text-gray-500">
                {stage.count} ({stage.percentage}%)
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <svg
            className="animate-spin h-10 w-10 text-blue-600 mx-auto mb-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <p className="text-gray-600">Loading your analytics data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col space-y-6 p-1 overflow-auto">
      <div className="bg-white rounded-lg shadow border border-gray-200 p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Your Performance Analytics</h1>
            <p className="text-gray-600">Track your recruiting metrics and performance</p>
          </div>
          
          <div className="flex items-center space-x-2">
            <label htmlFor="timeRange" className="text-sm text-gray-600">
              Time Period:
            </label>
            <Select

              value={timeRange}
              onValueChange={setTimeRange}

            >
              <option value="last7days">Last 7 Days</option>
              <option value="last30days">Last 30 Days</option>
              <option value="last90days">Last 90 Days</option>
            </Select>
            <Button
              variant="outline"
              onClick={() => {
                setTimeRange(prev => prev);
                setIsLoading(true);
              }}
              className="ml-2"
            >
              Refresh
            </Button>
          </div>
        </div>
        
        {analyticsData ? (
          <div className="space-y-8">
            {/* Key metrics */}
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-4">Your Key Metrics</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                  className="bg-blue-50 rounded-lg p-4 border border-blue-100"
                >
                  <div className="text-sm text-blue-600 font-medium">Active Requirements</div>
                  <div className="text-2xl font-bold text-gray-900 mt-1">
                    {analyticsData?.summary?.activeRequirements || 0}
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                  className="bg-indigo-50 rounded-lg p-4 border border-indigo-100"
                >
                  <div className="text-sm text-indigo-600 font-medium">Profiles Reviewed</div>
                  <div className="text-2xl font-bold text-gray-900 mt-1">
                    {analyticsData?.summary?.profilesReviewed || 0}
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.3 }}
                  className="bg-purple-50 rounded-lg p-4 border border-purple-100"
                >
                  <div className="text-sm text-purple-600 font-medium">Interviews</div>
                  <div className="text-2xl font-bold text-gray-900 mt-1">
                    {analyticsData?.summary?.interviewsScheduled || 0}
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.4 }}
                  className="bg-amber-50 rounded-lg p-4 border border-amber-100"
                >
                  <div className="text-sm text-amber-600 font-medium">Offers Extended</div>
                  <div className="text-2xl font-bold text-gray-900 mt-1">
                    {analyticsData?.summary?.offersExtended || 0}
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.5 }}
                  className="bg-green-50 rounded-lg p-4 border border-green-100"
                >
                  <div className="text-sm text-green-600 font-medium">Hires</div>
                  <div className="text-2xl font-bold text-gray-900 mt-1">
                    {analyticsData?.summary?.hires || 0}
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.6 }}
                  className="bg-rose-50 rounded-lg p-4 border border-rose-100"
                >
                  <div className="text-sm text-rose-600 font-medium">Conversion Rate</div>
                  <div className="text-2xl font-bold text-gray-900 mt-1">
                    {((analyticsData?.summary?.conversionRate || 0) * 100).toFixed(1)}%
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Weekly activity chart */}
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-3">Weekly Activity</h2>
              {analyticsData && useMemo(() => renderActivityChart(analyticsData.weeklyActivity || []), [analyticsData?.weeklyActivity])}
            </div>
            
            {/* Candidate pipeline */}
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-3">Candidate Pipeline</h2>
              {analyticsData && useMemo(() => renderPipeline(analyticsData.candidateStages || []), [analyticsData?.candidateStages])}
            </div>
            
            {/* Requirements breakdown */}
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-3">Requirements Breakdown</h2>
              <div className="overflow-x-auto rounded-lg border">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Job Title
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Department
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Profiles
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Interviews
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Offers
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Hires
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {analyticsData && analyticsData.requirementsBreakdown.map((requirement, index) => (
                      <motion.tr 
                        key={requirement.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        className="hover:bg-gray-50"
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{requirement.title}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {requirement.department}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {requirement.profilesReviewed}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {requirement.interviewsScheduled}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {requirement.offersExtended}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {requirement.hires}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            requirement.status === 'Active' 
                              ? 'bg-green-100 text-green-800' 
                              : requirement.status === 'On Hold' 
                              ? 'bg-amber-100 text-amber-800' 
                              : requirement.status === 'Filled'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {requirement.status}
                          </span>
                        </td>
                      </motion.tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
}
