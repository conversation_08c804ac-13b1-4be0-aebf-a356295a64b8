import { useState } from "react";

// Sample client companies data
const clientCompanies = [
  { id: 1, name: "Acme Corp" },
  { id: 2, name: "TechGiant" },
  { id: 3, name: "Innovate Inc" },
  { id: 4, name: "Global Systems" },
  { id: 5, name: "NextGen" },
  { id: 6, name: "Future Tech" },
  { id: 7, name: "Apex Solutions" },
  { id: 8, name: "Quantum Industries" },
];

// Sample roles data
const roles = [
  "Frontend Developer",
  "Backend Engineer",
  "Full Stack Developer",
  "DevOps Engineer",
  "UI/UX Designer",
  "Product Manager",
  "QA Engineer",
  "Data Scientist",
  "Mobile Developer",
  "Cloud Architect",
];

// Sample job types
const jobTypes = [
  "Full-time",
  "Part-time",
  "Contract",
  "Temporary",
  "Internship",
  "Remote",
];

// Sample modes of work
const modesOfWork = ["On-site", "Remote", "Hybrid", "Flexible"];

// Sample shift timings
const shiftTimings = [
  "Day Shift (9 AM - 6 PM)",
  "Night Shift (6 PM - 3 AM)",
  "Rotating Shift",
  "Flexible Hours",
  "US Shift (6 PM - 3 AM)",
  "UK Shift (2 PM - 11 PM)",
];

// Sample job statuses
const jobStatuses = ["Open", "Urgent", "On Hold", "Closed", "Filled"];

// Sample recruiters
const recruiters = [
  { id: 1, name: "John Smith" },
  { id: 2, name: "<PERSON> <PERSON>" },
  { id: 3, name: "<PERSON> Brown" },
  { id: 4, name: "Emily <PERSON>" },
  { id: 5, name: "David <PERSON>" },
];

// Interface for form data
interface JobFormData {
  client: string;
  role: string;
  skills: string;
  location: string;
  modeOfWork: string;
  maxExperience: string;
  minExperience: string;
  maxBudget: string;
  minBudget: string;
  noticePeriod: string;
  jobType: string;
  numberOfPositions: string;
  shiftTimings: string;
  jobStatus: string;
  recruiter: string;
  detailedJD: File | null;
  jobDescription: string;
}

export function JobAssignments() {
  const [formData, setFormData] = useState<JobFormData>({
    client: "",
    role: "",
    skills: "",
    location: "",
    modeOfWork: "",
    maxExperience: "",
    minExperience: "",
    maxBudget: "",
    minBudget: "",
    noticePeriod: "",
    jobType: "",
    numberOfPositions: "",
    shiftTimings: "",
    jobStatus: "",
    recruiter: "",
    detailedJD: null,
    jobDescription: "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFormData({
        ...formData,
        detailedJD: e.target.files[0],
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    alert("Job posted successfully!");
    setFormData({
      client: "",
      role: "",
      skills: "",
      location: "",
      modeOfWork: "",
      maxExperience: "",
      minExperience: "",
      maxBudget: "",
      minBudget: "",
      noticePeriod: "",
      jobType: "",
      numberOfPositions: "",
      shiftTimings: "",
      jobStatus: "",
      recruiter: "",
      detailedJD: null,
      jobDescription: "",
    });
  };

  return (
    <div className="w-full max-h-[calc(100vh-200px)] bg-gray-50">
      <div className="w-full bg-white p-6">
        <div className="w-full max-h-[calc(100vh-200px)] overflow-y-auto">
          <form onSubmit={handleSubmit} className="w-full max-w-none">
            <div className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-2 gap-x-6 gap-y-4 w-full">
                <div className="space-y-1 w-full">
                  <label
                    htmlFor="client"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Client:
                  </label>
                  <select
                    id="client"
                    name="client"
                    required
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.client}
                    onChange={handleInputChange}
                  >
                    <option value="">
                      Select an Existing Client or Enter a New Client
                    </option>
                    {clientCompanies.map((company) => (
                      <option key={company.id} value={company.name}>
                        {company.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="role"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Role:
                  </label>
                  <select
                    id="role"
                    name="role"
                    required
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.role}
                    onChange={handleInputChange}
                  >
                    <option value="">
                      Select an Existing Role or Enter a New Role
                    </option>
                    {roles.map((role, index) => (
                      <option key={index} value={role}>
                        {role}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="skills"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Skills:
                  </label>
                  <input
                    type="text"
                    id="skills"
                    name="skills"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.skills}
                    onChange={handleInputChange}
                    placeholder="e.g., JavaScript, React, Node.js"
                  />
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="numberOfPositions"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * No Of Positions:
                  </label>
                  <input
                    type="number"
                    id="numberOfPositions"
                    name="numberOfPositions"
                    required
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.numberOfPositions}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="location"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Location:
                  </label>
                  <input
                    type="text"
                    id="location"
                    name="location"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.location}
                    onChange={handleInputChange}
                    placeholder="e.g., New York, Remote"
                  />
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="jobType"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Job Type:
                  </label>
                  <select
                    id="jobType"
                    name="jobType"
                    required
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.jobType}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Job Type</option>
                    {jobTypes.map((type, index) => (
                      <option key={index} value={type}>
                        {type}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="modeOfWork"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Mode of Work:
                  </label>
                  <select
                    id="modeOfWork"
                    name="modeOfWork"
                    required
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.modeOfWork}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Mode of Work</option>
                    {modesOfWork.map((mode, index) => (
                      <option key={index} value={mode}>
                        {mode}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="minExperience"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Minimum Experience:
                  </label>
                  <input
                    type="number"
                    id="minExperience"
                    name="minExperience"
                    required
                    min="0"
                    step="0.5"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.minExperience}
                    onChange={handleInputChange}
                    placeholder="Years"
                  />
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="maxExperience"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Maximum Experience:
                  </label>
                  <input
                    type="number"
                    id="maxExperience"
                    name="maxExperience"
                    required
                    min="0"
                    step="0.5"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.maxExperience}
                    onChange={handleInputChange}
                    placeholder="Years"
                  />
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="minBudget"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Minimum Budget:
                  </label>
                  <div className="flex rounded-md shadow-sm w-full">
                    <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                      INR (₹)
                    </span>
                    <input
                      type="number"
                      id="minBudget"
                      name="minBudget"
                      required
                      min="0"
                      className="flex-1 w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.minBudget}
                      onChange={handleInputChange}
                      placeholder="Numeric"
                    />
                  </div>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="maxBudget"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Maximum Budget:
                  </label>
                  <div className="flex rounded-md shadow-sm w-full">
                    <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                      INR (₹)
                    </span>
                    <input
                      type="number"
                      id="maxBudget"
                      name="maxBudget"
                      required
                      min="0"
                      className="flex-1 w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.maxBudget}
                      onChange={handleInputChange}
                      placeholder="Numeric"
                    />
                  </div>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="shiftTimings"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Shift Timings:
                  </label>
                  <select
                    id="shiftTimings"
                    name="shiftTimings"
                    required
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.shiftTimings}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Shift Timings</option>
                    {shiftTimings.map((shift, index) => (
                      <option key={index} value={shift}>
                        {shift}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="noticePeriod"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Notice Period:
                  </label>
                  <input
                    type="text"
                    id="noticePeriod"
                    name="noticePeriod"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.noticePeriod}
                    onChange={handleInputChange}
                    placeholder="e.g., 30 days, 60 days"
                  />
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="jobStatus"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Job Status:
                  </label>
                  <select
                    id="jobStatus"
                    name="jobStatus"
                    required
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.jobStatus}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Job Status</option>
                    {jobStatuses.map((status, index) => (
                      <option key={index} value={status}>
                        {status}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="recruiter"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Recruiter:
                  </label>
                  <select
                    id="recruiter"
                    name="recruiter"
                    required
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.recruiter}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Recruiter</option>
                    {recruiters.map((recruiter) => (
                      <option key={recruiter.id} value={recruiter.name}>
                        {recruiter.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="detailedJD"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Detailed JD:
                  </label>
                  <div className="flex items-center space-x-3">
                    <label
                      htmlFor="jd-upload"
                      className="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Choose file
                      <input
                        id="jd-upload"
                        name="jd-upload"
                        type="file"
                        className="sr-only"
                        accept=".pdf,.doc,.docx"
                        onChange={handleFileChange}
                      />
                    </label>
                    <span className="text-sm text-gray-500 flex-1">
                      {formData.detailedJD
                        ? formData.detailedJD.name
                        : "No file chosen"}
                    </span>
                    {formData.detailedJD && (
                      <button
                        type="button"
                        className="text-red-600 hover:text-red-800 text-sm"
                        onClick={() =>
                          setFormData({ ...formData, detailedJD: null })
                        }
                      >
                        Clear
                      </button>
                    )}
                  </div>
                </div>
              </div>

              <div className="space-y-1 w-full">
                <label
                  htmlFor="jobDescription"
                  className="block text-sm font-medium text-gray-700"
                >
                  Detailed Job Description:
                </label>
                <textarea
                  id="jobDescription"
                  name="jobDescription"
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  value={formData.jobDescription}
                  onChange={handleInputChange}
                  placeholder="Enter detailed job description here..."
                />
              </div>
            </div>
          </form>
        </div>
        {/* Submit Button outside form */}
        <div className="w-full p-4 flex justify-end mt-4 bg-white shadow-top">
          <button
            type="button"
            onClick={handleSubmit}
            className="inline-flex justify-center py-2 px-6 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            Post Job
          </button>
        </div>
      </div>
    </div>
  );
}
